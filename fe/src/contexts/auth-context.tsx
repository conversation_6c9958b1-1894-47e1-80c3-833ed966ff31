import { createContext, useContext, useState, type ReactNode } from 'react';
import type { UserInfo, AuthenticationResponse } from '@/lib/types/api';
import { setAuthToken } from '@/lib/api/client';
import { clearAllQueries } from '@/lib/query-client';

// Type definitions from decoupled API types
export type User = UserInfo;
export type { AuthenticationResponse };

// Authentication status enum
export enum AuthStatus {
  LOADING = 'loading',
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
}

// Authentication state interface
export interface AuthState {
  user: User | null;
  accessToken: string | null;
  status: AuthStatus;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasInitialized: boolean;
}

// Authentication context interface
export interface AuthContextType extends AuthState {
  clearAuth: () => void;
  setInitialized: () => void;
  updateAuthState: (response: AuthenticationResponse) => void;
}

// Create the authentication context
const AuthContext = createContext<AuthContextType | undefined>(undefined);


// Authentication provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    accessToken: null,
    status: AuthStatus.LOADING,
    isAuthenticated: false,
    isLoading: true,
    hasInitialized: false,
  });

  // Update auth state with authentication response
  const updateAuthState = (response: AuthenticationResponse) => {
    const { access_token, user } = response;

    if (!access_token || !user) {
      throw new Error('Invalid authentication response');
    }
    // Set token in API client
    setAuthToken(access_token);

    // Update state
    setAuthState({
      user,
      accessToken: access_token,
      status: AuthStatus.AUTHENTICATED,
      isAuthenticated: true,
      isLoading: false,
      hasInitialized: true,
    });
  };

  // Clear auth function - for error handling
  const clearAuth = () => {
    // Clear token from API client
    setAuthToken(null);

    // Clear all React Query cache
    clearAllQueries();

    // Update state
    setAuthState({
      user: null,
      accessToken: null,
      status: AuthStatus.UNAUTHENTICATED,
      isAuthenticated: false,
      isLoading: false,
      hasInitialized: true,
    });
  };

  // Set initialized function - for auth layout
  const setInitialized = () => {
    setAuthState(prev => ({
      ...prev,
      hasInitialized: true,
      isLoading: false,
    }));
  };

  const contextValue: AuthContextType = {
    ...authState,
    clearAuth,
    setInitialized,
    updateAuthState,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use the auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

// Helper hook to check if user is authenticated
export function useIsAuthenticated(): boolean {
  const { isAuthenticated } = useAuth();
  return isAuthenticated;
}

// Helper hook to get current user
export function useCurrentUser(): User | null {
  const { user } = useAuth();
  return user;
}
