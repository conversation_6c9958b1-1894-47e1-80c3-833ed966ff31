import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching a single post by ID.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically scopes results to the current account (multi-tenancy).
 * Returns the post with all media, reviewers, and permissions.
 * Only returns posts from hubs where the user is a participant.
 * 
 * @param postId - Post ID to fetch
 * @param options - Query options including enabled and staleTime
 */
export function usePost(
  postId: number | null, 
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/posts/{postId}', {
    params: {
      path: { postId: postId! },
    },
  }, {
    enabled: options?.enabled !== false && !!postId,
    // Cache data for 2 minutes (posts change frequently during review process)
    staleTime: options?.staleTime ?? 2 * 60 * 1000,
  });
}
