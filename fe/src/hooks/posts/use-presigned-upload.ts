import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';

interface FileUploadRequest {
  file_name: string;
  content_type: string;
  max_file_size?: number;
}

interface PresignedUploadResponse {
  presigned_url: string;
  final_url: string;
  key: string;
  content_type: string;
  max_file_size: number;
  expires_in_minutes: number;
}

interface BatchPresignedUploadResponse {
  upload_urls: PresignedUploadResponse[];
  expires_in_minutes: number;
}

interface UploadResult {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
  type: string;
}

interface FileUploadProgress {
  fileId: string;
  progress: number;
  isComplete: boolean;
  error?: string;
}

/**
 * Custom hook for uploading media files using presigned URLs.
 * Supports concurrent uploads with individual progress tracking.
 * 
 * File constraints (enforced by backend):
 * - Images: max 10MB, types: jpeg, png, gif, webp
 * - Videos: max 100MB, types: mp4, quicktime, avi
 */
export function usePresignedUpload() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<Map<string, FileUploadProgress>>(new Map());
  const { accessToken } = useAuth();

  const validateFile = (file: File): void => {
    const maxImageSize = 10 * 1024 * 1024; // 10MB
    const maxVideoSize = 100 * 1024 * 1024; // 100MB
    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const allowedVideoTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo'];

    const isImage = allowedImageTypes.includes(file.type);
    const isVideo = allowedVideoTypes.includes(file.type);

    if (!isImage && !isVideo) {
      throw new Error('File type not supported. Please upload images (JPEG, PNG, GIF, WebP) or videos (MP4, MOV, AVI).');
    }

    if (isImage && file.size > maxImageSize) {
      throw new Error('Image file size must be less than 10MB.');
    }

    if (isVideo && file.size > maxVideoSize) {
      throw new Error('Video file size must be less than 100MB.');
    }
  };

  const generatePresignedUrls = useCallback(async (files: File[]): Promise<PresignedUploadResponse[]> => {
    const fileRequests: FileUploadRequest[] = files.map(file => ({
      file_name: file.name,
      content_type: file.type,
      max_file_size: file.size
    }));

    const response = await fetch('/api/posts/media/batch-presigned-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify({ files: fileRequests }),
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData.error?.message) {
          errorMessage = errorData.error.message;
        }
      } catch {
        // If response is not JSON, use the default error message
      }
      throw new Error(errorMessage);
    }

    const result: BatchPresignedUploadResponse = await response.json();
    return result.upload_urls;
  }, [accessToken]);

  const uploadFileToS3 = async (
    file: File,
    presignedUrl: string,
    fileId: string
  ): Promise<void> => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          setUploadProgress(prev => new Map(prev.set(fileId, {
            fileId,
            progress: Math.round(percentComplete),
            isComplete: false
          })));
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          setUploadProgress(prev => new Map(prev.set(fileId, {
            fileId,
            progress: 100,
            isComplete: true
          })));
          resolve();
        } else {
          const error = `HTTP ${xhr.status}: ${xhr.statusText}`;
          setUploadProgress(prev => new Map(prev.set(fileId, {
            fileId,
            progress: 0,
            isComplete: false,
            error
          })));
          reject(new Error(error));
        }
      });

      xhr.addEventListener('error', () => {
        const error = 'Network error occurred during upload';
        setUploadProgress(prev => new Map(prev.set(fileId, {
          fileId,
          progress: 0,
          isComplete: false,
          error
        })));
        reject(new Error(error));
      });

      xhr.open('PUT', presignedUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.send(file);
    });
  };

  const createMediaRecord = useCallback(async (file: File, finalUrl: string): Promise<UploadResult> => {
    const response = await fetch('/api/posts/media/create-record', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        file_url: finalUrl,
        filename: file.name,
        file_size: file.size,
        mime_type: file.type,
      }),
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData.error?.message) {
          errorMessage = errorData.error.message;
        }
      } catch {
        // If response is not JSON, use the default error message
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    return {
      url: result.url,
      filename: result.filename,
      size: result.size,
      mimeType: result.mime_type,
      type: result.type,
    };
  }, [accessToken]);

  const uploadFiles = useCallback(async (files: File[]): Promise<UploadResult[]> => {
    setIsLoading(true);
    setError(null);
    setUploadProgress(new Map());

    try {
      // Validate all files first
      files.forEach(validateFile);

      // Generate presigned URLs for all files
      const presignedUrls = await generatePresignedUrls(files);

      if (presignedUrls.length !== files.length) {
        throw new Error('Mismatch between files and presigned URLs');
      }

      // Upload all files concurrently
      const uploadPromises = files.map(async (file, index) => {
        const fileId = `${file.name}-${Date.now()}-${index}`;
        const presignedData = presignedUrls[index];

        try {
          // First upload to S3
          await uploadFileToS3(file, presignedData.presigned_url, fileId);

          // Then create media record in backend
          const result = await createMediaRecord(file, presignedData.final_url);

          return result;
        } catch (error) {
          throw new Error(`Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      const results = await Promise.all(uploadPromises);
      return results;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload files';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [createMediaRecord, generatePresignedUrls]);

  const uploadSingleFile = useCallback(async (file: File): Promise<UploadResult> => {
    const results = await uploadFiles([file]);
    return results[0];
  }, [uploadFiles]);

  const reset = useCallback(() => {
    setError(null);
    setUploadProgress(new Map());
  }, []);

  const getFileProgress = useCallback((fileId: string): FileUploadProgress | undefined => {
    return uploadProgress.get(fileId);
  }, [uploadProgress]);

  return {
    uploadFiles,
    uploadSingleFile,
    isLoading,
    error,
    uploadProgress,
    getFileProgress,
    reset,
  };
}
