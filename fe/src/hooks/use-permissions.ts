import { useAuth } from '@/contexts/auth-context'

/**
 * Custom hook for checking user permissions and roles.
 * Provides utility functions to check if the current user has specific permissions.
 */
export function usePermissions() {
  const { user } = useAuth()

  /**
   * Check if the current user has permission to create posts.
   * Only ADMIN and USER roles can create posts.
   */
  const canCreatePost = () => {
    return user && (user.role === 'ADMIN' || user.role === 'USER')
  }

  /**
   * Check if the current user can edit a specific post.
   * ADMIN can edit any post, USER can only edit their own posts.
   */
  const canEditPost = (postCreatorId?: number) => {
    if (!user) return false
    
    // Admin can edit any post
    if (user.role === 'ADMIN') return true
    
    // Regular users can only edit their own posts
    if (user.role === 'USER' && postCreatorId) {
      return user.id === postCreatorId
    }
    
    return false
  }

  /**
   * Check if the current user can delete a specific post.
   * Same rules as editing - ADMIN can delete any post, USER can only delete their own.
   */
  const canDeletePost = (postCreatorId?: number) => {
    return canEditPost(postCreatorId)
  }

  /**
   * Check if the current user is an admin.
   */
  const isAdmin = () => {
    return user?.role === 'ADMIN'
  }

  /**
   * Check if the current user is a regular user.
   */
  const isUser = () => {
    return user?.role === 'USER'
  }

  /**
   * Get the current user's role.
   */
  const getUserRole = () => {
    return user?.role
  }

  /**
   * Get the current user's ID.
   */
  const getUserId = () => {
    return user?.id
  }

  return {
    canCreatePost,
    canEditPost,
    canDeletePost,
    isAdmin,
    isUser,
    getUserRole,
    getUserId,
    user
  }
}
