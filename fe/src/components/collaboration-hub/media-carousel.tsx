import { useState } from "react"
import { ChevronLeft, ChevronRight, Play, Image as ImageIcon } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { cn } from "@/lib/utils"

interface MediaItem {
  id: string
  type: 'image' | 'video'
  url: string
  thumbnail?: string
  alt?: string
}

interface MediaCarouselProps {
  media: MediaItem[]
  className?: string
}

export function MediaCarousel({ media, className }: MediaCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const { t, keys } = useTranslations()

  if (!media || media.length === 0) {
    return (
      <div className={cn("aspect-square bg-muted rounded-lg flex items-center justify-center", className)}>
        <ImageIcon className="h-12 w-12 text-muted-foreground" />
      </div>
    )
  }

  const currentMedia = media[currentIndex]
  const hasMultiple = media.length > 1

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? media.length - 1 : prev - 1))
  }

  const goToNext = () => {
    setCurrentIndex((prev) => (prev === media.length - 1 ? 0 : prev + 1))
  }

  return (
    <div className={cn("relative aspect-square bg-black rounded-lg overflow-hidden group", className)}>
      {/* Media Content */}
      <div className="relative w-full h-full">
        {currentMedia.type === 'video' ? (
          <div className="relative w-full h-full">
            <img
              src={currentMedia.thumbnail || currentMedia.url}
              alt={currentMedia.alt || t(keys.collaborationHubs.mediaCarousel.mediaAlt, { index: currentIndex + 1 })}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-black/50 rounded-full p-3">
                <Play className="h-6 w-6 text-white fill-white" />
              </div>
            </div>
          </div>
        ) : (
          <img
            src={currentMedia.url}
            alt={currentMedia.alt || t(keys.collaborationHubs.mediaCarousel.mediaAlt, { index: currentIndex + 1 })}
            className="w-full h-full object-cover"
          />
        )}
      </div>

      {/* Navigation Arrows */}
      {hasMultiple && (
        <>
          <Button
            variant="ghost"
            size="sm"
            className="absolute left-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 bg-black/20 hover:bg-black/40 text-white opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={goToPrevious}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 bg-black/20 hover:bg-black/40 text-white opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={goToNext}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </>
      )}

      {/* Media Counter */}
      {hasMultiple && (
        <Badge 
          variant="secondary" 
          className="absolute top-2 right-2 bg-black/50 text-white border-0"
        >
          {currentIndex + 1}/{media.length}
        </Badge>
      )}

      {/* Dots Indicator */}
      {hasMultiple && (
        <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
          {media.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all",
                index === currentIndex 
                  ? "bg-white" 
                  : "bg-white/50 hover:bg-white/75"
              )}
            />
          ))}
        </div>
      )}

      {/* Media Type Badge */}
      {currentMedia.type === 'video' && (
        <Badge
          variant="secondary"
          className="absolute top-2 left-2 bg-black/50 text-white border-0"
        >
          {t(keys.collaborationHubs.mediaCarousel.video)}
        </Badge>
      )}
    </div>
  )
}
