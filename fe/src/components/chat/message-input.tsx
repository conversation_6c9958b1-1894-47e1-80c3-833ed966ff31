import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Send, Paperclip, Smile, X, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useWebSocket } from '@/lib/websocket';
import { useSendMessage } from '@/hooks/chat';
import { useUploadChatAttachment } from '@/hooks/chat';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface MessageInputProps {
  channelId: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

interface AttachmentPreview {
  file: File;
  url: string;
  uploading: boolean;
  uploadedUrl?: string;
  uploadedMetadata?: {
    url: string;
    filename: string;
    size: number;
    content_type: string;
    type: string;
  };
  error?: string;
}

export function MessageInput({ channelId, placeholder, disabled, className }: MessageInputProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const { sendMessage: sendWebSocketMessage, sendTypingIndicator, isConnected } = useWebSocket();
  const sendMessageMutation = useSendMessage(channelId);
  const { uploadFile, isLoading: isUploading, progress } = useUploadChatAttachment();
  
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<AttachmentPreview[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Handle typing indicators
  const handleTyping = useCallback(() => {
    if (!isTyping && isConnected) {
      setIsTyping(true);
      sendTypingIndicator(channelId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 2000);
  }, [isTyping, isConnected, sendTypingIndicator, channelId]);

  // Cleanup typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const handleFileSelect = useCallback(async (files: FileList) => {
    const newAttachments: AttachmentPreview[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const preview: AttachmentPreview = {
        file,
        url: URL.createObjectURL(file),
        uploading: true,
      };
      newAttachments.push(preview);
    }

    setAttachments(prev => [...prev, ...newAttachments]);

    // Upload files
    for (let i = 0; i < newAttachments.length; i++) {
      const attachment = newAttachments[i];
      try {
        const result = await uploadFile(attachment.file);
        setAttachments(prev => prev.map(a =>
          a.file === attachment.file
            ? {
                ...a,
                uploading: false,
                uploadedUrl: result.url,
                uploadedMetadata: {
                  url: result.url,
                  filename: result.filename,
                  size: result.size,
                  content_type: result.mimeType,
                  type: result.type
                }
              }
            : a
        ));
      } catch (error) {
        setAttachments(prev => prev.map(a => 
          a.file === attachment.file 
            ? { ...a, uploading: false, error: error instanceof Error ? error.message : 'Upload failed' }
            : a
        ));
      }
    }
  }, [uploadFile]);

  const handleRemoveAttachment = useCallback((index: number) => {
    setAttachments(prev => {
      const attachment = prev[index];
      URL.revokeObjectURL(attachment.url);
      return prev.filter((_, i) => i !== index);
    });
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!message.trim() && attachments.length === 0) return;
    if (disabled || sendMessageMutation.isPending) return;

    // Check if any attachments are still uploading
    const uploadingAttachments = attachments.filter(a => a.uploading);
    if (uploadingAttachments.length > 0) {
      return; // Wait for uploads to complete
    }

    // Check if any attachments failed to upload
    const failedAttachments = attachments.filter(a => a.error);
    if (failedAttachments.length > 0) {
      return; // Don't send if there are failed uploads
    }

    const attachmentUris = attachments
      .filter(a => a.uploadedUrl)
      .map(a => a.uploadedUrl!);

    const attachmentMetadata = attachments
      .filter(a => a.uploadedMetadata)
      .map(a => a.uploadedMetadata!);

    try {
      // Use REST API when attachments are present to send complete metadata
      // WebSocket only supports attachment URIs, not full metadata
      if (isConnected && attachmentMetadata.length === 0) {
        // Send via WebSocket for real-time delivery (text-only messages)
        sendWebSocketMessage(channelId, message.trim(), attachmentUris);
      } else {
        // Use REST API for messages with attachments or when WebSocket is not connected
        await sendMessageMutation.mutateAsync({
          params: { path: { channelId } },
          body: {
            content: message.trim() || "", // Ensure content is never null
            attachment_uris: attachmentUris, // Simple URIs for backward compatibility
            // Note: Enhanced attachment metadata is handled by the backend
          },
        });
      }

      // Clear input and attachments
      setMessage('');
      setAttachments(prev => {
        prev.forEach(a => URL.revokeObjectURL(a.url));
        return [];
      });
      setIsTyping(false);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, [message, attachments, disabled, sendMessageMutation, isConnected, sendWebSocketMessage, channelId]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
    handleTyping();
  }, [handleTyping]);

  const canSend = (message.trim() || attachments.some(a => a.uploadedUrl)) && 
                 !sendMessageMutation.isPending && 
                 !attachments.some(a => a.uploading) &&
                 !disabled;

  return (
    <div className={cn("space-y-2", className)}>
      {/* Attachment Previews */}
      {attachments.length > 0 && (
        <div className="flex flex-wrap gap-2 p-2 border rounded-lg bg-muted/30 max-h-32 overflow-y-auto">
          {attachments.map((attachment, index) => (
            <div key={index} className="relative flex items-center gap-2 p-2 border rounded bg-background">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{attachment.file.name}</p>
                {attachment.uploading && (
                  <div className="space-y-1">
                    <Progress value={progress} className="h-1" />
                    <p className="text-xs text-muted-foreground">{t(keys.collaborationHubs.chat.uploading)}</p>
                  </div>
                )}
                {attachment.error && (
                  <p className="text-xs text-destructive">{attachment.error}</p>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleRemoveAttachment(index)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Message Input */}
      <div className="flex items-end gap-3">
        <div className="flex-1 flex items-center gap-2 px-4 py-3 border rounded-lg bg-background">
          <Input
            placeholder={placeholder || t(keys.collaborationHubs.chat.messagePlaceholder, { channelName: 'channel' })}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            disabled={disabled}
            className="border-0 focus-visible:ring-0 px-0 py-0 h-auto bg-transparent placeholder:text-muted-foreground"
          />

          {/* File Upload Button */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0 hover:bg-muted"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || isUploading}
            title={t(keys.collaborationHubs.chat.attachFile)}
          >
            {isUploading ? <Upload className="h-4 w-4 animate-spin" /> : <Paperclip className="h-4 w-4" />}
          </Button>

          {/* Emoji Button (placeholder for future implementation) */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0 hover:bg-muted"
            disabled={disabled}
            title={t(keys.collaborationHubs.chat.addEmoji)}
          >
            <Smile className="h-4 w-4" />
          </Button>
        </div>

        <Button
          onClick={handleSendMessage}
          disabled={!canSend}
          className="shrink-0 h-12 px-4"
          size="default"
        >
          <Send className="h-4 w-4" />
          {!isMobile && <span className="ml-2 hidden sm:inline">{t(keys.collaborationHubs.chat.send)}</span>}
        </Button>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,video/*,.pdf,.doc,.docx,.txt"
        onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
        className="hidden"
      />
    </div>
  );
}
