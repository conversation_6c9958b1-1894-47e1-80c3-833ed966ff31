import { useState } from "react"
import { MessageCircle, MoreHorizontal, Edit, Trash2, ChevronDown, ChevronUp, CheckCircle, AlertCircle, Clock, User, Shield } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { MediaCarousel } from "@/components/collaboration-hub/media-carousel"
import { PostReviewForm } from "./post-review-form"
import { PostReviewStatusBadge } from "./post-review-status-badge"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { useDeletePost } from "@/hooks/posts"
import { usePostReviews, usePostReviewStats } from "@/lib/api/hooks/usePostReviews"
import { getPostPermissions, getReviewActions } from "@/lib/utils/post-permissions"
import { toast } from "sonner"
import type { PostListItemResponse, MediaItem, PostReviewResponse } from "@/lib/types/api"

interface PostCardProps {
  post: PostListItemResponse
  onEdit?: (postId: number) => void
  showReviewPanel?: boolean
  className?: string
}

export function PostCard({ post, onEdit, showReviewPanel = false, className }: PostCardProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [reviewPanelExpanded, setReviewPanelExpanded] = useState(false)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const { t, keys } = useTranslations()
  const deletePostMutation = useDeletePost()

  // Get permissions and actions from post data
  const permissions = getPostPermissions(post)
  const actions = getReviewActions(post)

  // Fetch review data if review panel is shown
  const postId = post.id!
  const { data: reviewsData, isLoading: reviewsLoading } = usePostReviews(postId, {
    enabled: showReviewPanel && actions.showReviewPanel
  })
  const reviewStats = usePostReviewStats(postId, {
    enabled: showReviewPanel && actions.showReviewPanel
  })

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "approved": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "rework": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'rework':
        return <AlertCircle className="h-4 w-4 text-orange-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const formatReviewerName = (review: PostReviewResponse) => {
    return review.reviewer_name || review.reviewer_email || 'Unknown Reviewer'
  }

  const getRoleIcon = (review: PostReviewResponse) => {
    if (review.is_external) {
      return <User className="h-3 w-3 text-gray-500" title="External Reviewer" />
    }
    return <Shield className="h-3 w-3 text-green-600" title="Internal Reviewer" />
  }

  const getInitials = (name?: string) => {
    if (!name) return '??'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const highlightHashtagsAndMentions = (text: string) => {
    return text
      .replace(/\n/g, '<br>')
      .replace(
        /(#[^\s]+|@[^\s]+)/g,
        '<span class="text-blue-600 hover:text-blue-700 font-medium cursor-pointer">$1</span>'
      )
  }

  // Convert API MediaItem to MediaCarousel format
  const convertMediaItems = (mediaItems?: MediaItem[]) => {
    if (!mediaItems || mediaItems.length === 0) return []

    return mediaItems.map((item, index) => ({
      id: `media-${index}`,
      type: (item.type === 'video' ? 'video' : 'image') as 'image' | 'video',
      url: item.url || '',
      alt: `Media ${index + 1}`
    }))
  }

  // Use server-provided permissions instead of client-side calculation
  const canEdit = post.can_edit

  const handleEdit = () => {
    if (onEdit && post.id) {
      onEdit(post.id)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!post.id) return

    try {
      await deletePostMutation.mutateAsync({
        params: { path: { postId: post.id } }
      })

      toast.success(t(keys.collaborationHubs.posts.postDeleted))
      setDeleteDialogOpen(false)
    } catch (error) {
      console.error('Failed to delete post:', error)
      toast.error('Failed to delete post. Please try again.')
    }
  }

  const handleReviewSubmitted = () => {
    setShowReviewForm(false)
    setReviewPanelExpanded(true) // Keep panel expanded to show updated reviews
  }

  return (
    <>
      <Card className={className}>
        {/* Post Header */}
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={undefined} />
                <AvatarFallback className="text-sm">
                  {getInitials(post.creator?.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-sm">{post.creator?.name || 'Unknown'}</p>
                <div className="flex items-center gap-2">
                  <PostReviewStatusBadge
                    status={post.review_status as 'pending' | 'approved' | 'rework'}
                    reviewCount={{
                      completed: post.approved_by_count ?? 0,
                      total: post.assigned_reviewer_count ?? 0
                    }}
                    size="sm"
                    showProgress={true}
                  />
                  <span className="text-xs text-muted-foreground">
                    {formatDate(post.created_at)}
                  </span>
                </div>
              </div>
            </div>

            {canEdit && (
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    {t(keys.collaborationHubs.posts.actions.edit)}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setDeleteDialogOpen(true)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t(keys.collaborationHubs.posts.actions.delete)}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>

        {/* Post Content */}
        <CardContent className="space-y-4">
          {/* Media Display */}
          {post.media_uris && post.media_uris.length > 0 && (
            <MediaCarousel
              media={convertMediaItems(post.media_uris)}
              className="w-full"
            />
          )}

          {/* Caption */}
          {post.caption && (
            <div 
              className="text-sm leading-relaxed"
              dangerouslySetInnerHTML={{ 
                __html: highlightHashtagsAndMentions(post.caption) 
              }}
            />
          )}

          {/* My Review Status */}
          {permissions.isAssignedReviewer && post.my_review_status && (
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Your review:</span>
              <Badge
                variant="outline"
                className={getStatusColor(post.my_review_status)}
              >
                {post.my_review_status === 'pending'
                  ? t(keys.collaborationHubs.posts.pending)
                  : post.my_review_status === 'approved'
                  ? t(keys.collaborationHubs.posts.approved)
                  : t(keys.collaborationHubs.posts.rework)
                }
              </Badge>
            </div>
          )}

          {/* Post Stats */}
          <div className="flex items-center gap-4 pt-2 border-t">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <MessageCircle className="h-4 w-4" />
              <span>{post.comment_count || 0} {t(keys.collaborationHubs.posts.comments)}</span>
            </div>
            {(post.media_uris?.length ?? 0) > 0 && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>{post.media_uris?.length ?? 0} media</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Integrated Review Panel */}
      {showReviewPanel && actions.showReviewPanel && (
        <Card className="mt-4">
          <Collapsible open={reviewPanelExpanded} onOpenChange={setReviewPanelExpanded}>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(reviewStats?.overallStatus || 'pending')}
                    <div>
                      <h4 className="text-sm font-medium">
                        Review Status: {reviewStats?.overallStatus === 'pending' ? 'Pending' :
                                       reviewStats?.overallStatus === 'approved' ? 'Approved' : 'Needs Rework'}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {(reviewStats?.approvedReviews || 0) + (reviewStats?.reworkReviews || 0)} of {reviewStats?.totalReviewers || 0} reviewers completed
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {actions.canSubmitReview && (
                      <Button
                        size="sm"
                        variant={permissions.hasExistingReview ? "outline" : "default"}
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowReviewForm(!showReviewForm)
                        }}
                      >
                        {actions.reviewButtonText}
                      </Button>
                    )}
                    {reviewPanelExpanded ? (
                      <ChevronUp className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                </div>
              </CardHeader>
            </CollapsibleTrigger>

            <CollapsibleContent>
              <CardContent className="pt-0">
                {/* Review Form */}
                {showReviewForm && actions.canSubmitReview && (
                  <div className="mb-4">
                    <PostReviewForm
                      postId={postId}
                      existingReview={null} // We'll need to get this from a separate API call if needed
                      onSubmitted={handleReviewSubmitted}
                      onCancel={() => setShowReviewForm(false)}
                    />
                  </div>
                )}

                <Separator className="mb-4" />

                {/* Reviews List */}
                {reviewsLoading ? (
                  <div className="animate-pulse space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ) : reviewsData?.reviews && reviewsData.reviews.length > 0 ? (
                  <div className="space-y-3">
                    {reviewsData.reviews.map((review) => (
                      <div key={review.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {formatReviewerName(review).split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-sm font-medium">
                              {formatReviewerName(review)}
                            </span>
                            {getRoleIcon(review)}
                            {review.is_external && (
                              <Badge variant="secondary" className="text-xs">
                                External
                              </Badge>
                            )}
                            <Badge
                              variant="outline"
                              className={getStatusColor(review.decision)}
                            >
                              {review.decision === 'approved' ? 'Approved' :
                               review.decision === 'rework' ? 'Rework' : 'Pending'}
                            </Badge>
                          </div>

                          {review.comment && (
                            <p className="text-sm text-gray-700 mb-2 leading-relaxed">
                              {review.comment}
                            </p>
                          )}

                          {review.reviewed_at && (
                            <p className="text-xs text-muted-foreground">
                              Reviewed {new Date(review.reviewed_at).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No reviews yet
                  </p>
                )}
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.posts.confirmDelete)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.posts.deleteDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t(keys.common.cancel)}</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              disabled={deletePostMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deletePostMutation.isPending 
                ? t(keys.collaborationHubs.posts.deleting)
                : t(keys.collaborationHubs.posts.actions.delete)
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
