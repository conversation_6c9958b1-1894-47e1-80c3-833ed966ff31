/**
 * Posts components for the Collaboration Hub frontend.
 *
 * These components provide a comprehensive interface for post management
 * including creation, editing, media upload, and reviewer assignment.
 *
 * Main Components:
 * - PostFormDialog: Main dialog for creating and editing posts
 * - PostCard: Individual post display component with actions
 * - PostCardWithReviews: Enhanced post card with review functionality
 * - PostsList: Container for posts with infinite scroll
 * - PostCardSkeleton: Loading skeleton for post cards
 * - MediaUpload: Drag-and-drop media upload with progress tracking
 * - ReviewerMultiSelect: Multi-select component for assigning reviewers
 * - PostReviewPanel: Inline review panel for post reviews
 * - PostReviewForm: Form component for creating/updating reviews
 * - PostReviewStatusBadge: Status badge for review states
 *
 * All components follow established patterns:
 * - Mobile-responsive design using useIsMobile hook
 * - Form validation using react-hook-form and zod
 * - Proper loading states and error handling
 * - Integration with openapi-react-query hooks
 * - Consistent styling with shadcn/ui components
 */

export { PostFormDialog } from './post-form-dialog';
export { PostCard } from './post-card';
export { PostCardWithReviews, PostCardCompact } from './PostCardWithReviews';
export { PostsList } from './posts-list';
export { PostCardSkeleton } from './post-card-skeleton';
export { MediaUpload } from './media-upload';
export { ReviewerMultiSelect } from './reviewer-multi-select';
export { PostReviewPanel } from './PostReviewPanel';
export { PostReviewForm } from './PostReviewForm';
export { PostReviewStatusBadge, PostReviewStatusCompact, PostReviewStatusDetailed } from './PostReviewStatusBadge';
export type * from './types';
