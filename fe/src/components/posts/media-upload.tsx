import { useState, useCallback, useRef, useEffect } from 'react'
import { Upload, X, Video, GripVertical, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'
import { usePresignedUpload } from '@/hooks/posts'

interface MediaFile {
  id: string
  file?: File
  url: string
  type: 'image' | 'video'
  filename: string
  size: number
  isUploading?: boolean
  uploadProgress?: number
  error?: string
}

interface MediaUploadProps {
  value: string[]
  onChange: (urls: string[]) => void
  maxFiles?: number
  className?: string
  disabled?: boolean
}

export function MediaUpload({
  value = [],
  onChange,
  maxFiles = 10,
  className,
  disabled = false
}: MediaUploadProps) {
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { uploadFiles, isLoading: isUploading, uploadProgress } = usePresignedUpload()

  // Convert URLs to media files for display
  const initializeMediaFiles = useCallback(() => {
    const existingFiles = value.map((url, index) => ({
      id: `existing-${index}`,
      url,
      type: url.includes('video') || url.includes('.mp4') || url.includes('.mov') || url.includes('.avi') 
        ? 'video' as const 
        : 'image' as const,
      filename: url.split('/').pop() || 'media',
      size: 0,
    }))
    setMediaFiles(existingFiles)
  }, [value])

  // Initialize media files when value changes
  useEffect(() => {
    initializeMediaFiles()
  }, [initializeMediaFiles])

  // Update progress from the upload hook
  useEffect(() => {
    if (uploadProgress.size > 0) {
      setMediaFiles(prev => prev.map(media => {
        if (media.file) {
          const progressData = Array.from(uploadProgress.values()).find(p =>
            p.fileId.includes(media.file!.name)
          )
          if (progressData) {
            return {
              ...media,
              uploadProgress: progressData.progress,
              isUploading: !progressData.isComplete && !progressData.error,
              error: progressData.error
            }
          }
        }
        return media
      }))
    }
  }, [uploadProgress])

  const handleFiles = useCallback(async (files: FileList) => {
    if (disabled) return

    const fileArray = Array.from(files)
    const remainingSlots = maxFiles - mediaFiles.length
    const filesToProcess = fileArray.slice(0, remainingSlots)

    if (filesToProcess.length === 0) return

    // Create temporary media files for UI display
    const tempMediaFiles: MediaFile[] = filesToProcess.map((file, index) => ({
      id: `temp-${Date.now()}-${index}`,
      file,
      url: URL.createObjectURL(file),
      type: file.type.startsWith('video/') ? 'video' : 'image',
      filename: file.name,
      size: file.size,
      isUploading: true,
      uploadProgress: 0,
    }))

    // Add all temp files to state immediately
    setMediaFiles(prev => [...prev, ...tempMediaFiles])

    try {
      // Upload all files concurrently using presigned URLs
      const results = await uploadFiles(filesToProcess)

      // Update all files as completed
      setMediaFiles(prev => prev.map(media => {
        const tempIndex = tempMediaFiles.findIndex(temp => temp.id === media.id)
        if (tempIndex !== -1) {
          const result = results[tempIndex]
          return {
            ...media,
            url: result.url,
            isUploading: false,
            uploadProgress: 100,
            error: undefined,
          }
        }
        return media
      }))

      // Update the form value with all new URLs
      const newUrls = [...value, ...results.map(result => result.url)]
      onChange(newUrls)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'

      // Mark all temp files as failed
      setMediaFiles(prev => prev.map(media => {
        const isTempFile = tempMediaFiles.some(temp => temp.id === media.id)
        if (isTempFile) {
          return {
            ...media,
            isUploading: false,
            error: errorMessage,
          }
        }
        return media
      }))
    }
  }, [disabled, maxFiles, mediaFiles.length, uploadFiles, value, onChange])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled) return
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }, [disabled, handleFiles])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files)
    }
  }, [handleFiles])

  const removeMedia = useCallback((id: string) => {
    const mediaToRemove = mediaFiles.find(media => media.id === id)
    if (mediaToRemove && !mediaToRemove.isUploading) {
      setMediaFiles(prev => prev.filter(media => media.id !== id))
      
      // Update form value
      const newUrls = value.filter(url => url !== mediaToRemove.url)
      onChange(newUrls)
    }
  }, [mediaFiles, value, onChange])

  const retryUpload = useCallback(async (id: string) => {
    const mediaFile = mediaFiles.find(media => media.id === id)
    if (!mediaFile?.file || !mediaFile.error) return

    setMediaFiles(prev => prev.map(media =>
      media.id === id
        ? { ...media, isUploading: true, error: undefined, uploadProgress: 0 }
        : media
    ))

    try {
      const results = await uploadFiles([mediaFile.file])
      const result = results[0]

      setMediaFiles(prev => prev.map(media =>
        media.id === id
          ? {
              ...media,
              url: result.url,
              isUploading: false,
              uploadProgress: 100,
              error: undefined,
            }
          : media
      ))

      // Update the form value
      const newUrls = [...value, result.url]
      onChange(newUrls)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      setMediaFiles(prev => prev.map(media =>
        media.id === id
          ? { ...media, isUploading: false, error: errorMessage }
          : media
      ))
    }
  }, [mediaFiles, uploadFiles, value, onChange])

  const canAddMore = mediaFiles.length < maxFiles && !disabled

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      {canAddMore && (
        <div
          className={cn(
            "relative border-2 border-dashed rounded-lg p-6 text-center transition-colors",
            dragActive 
              ? "border-primary bg-primary/5" 
              : "border-muted-foreground/25 hover:border-muted-foreground/50",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/jpeg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/x-msvideo"
            onChange={handleInputChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={disabled}
          />
          
          <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-lg font-medium mb-2">
            Drop files here or click to upload
          </p>
          <p className="text-sm text-muted-foreground mb-4">
            Images (JPEG, PNG, GIF, WebP) up to 10MB<br />
            Videos (MP4, MOV, AVI) up to 100MB
          </p>
          <Button type="button" variant="outline" disabled={disabled}>
            Choose Files
          </Button>
          
          {maxFiles > 1 && (
            <p className="text-xs text-muted-foreground mt-2">
              {mediaFiles.length} of {maxFiles} files uploaded
            </p>
          )}
        </div>
      )}

      {/* Media Preview Grid */}
      {mediaFiles.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {mediaFiles.map((media) => (
            <div key={media.id} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                {media.type === 'image' ? (
                  <img
                    src={media.url}
                    alt={media.filename}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Video className="h-8 w-8 text-muted-foreground" />
                  </div>
                )}
                
                {/* Upload Progress Overlay */}
                {media.isUploading && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="text-center text-white">
                      <Progress 
                        value={media.uploadProgress || 0} 
                        className="w-16 mb-2" 
                      />
                      <p className="text-xs">Uploading...</p>
                    </div>
                  </div>
                )}
                
                {/* Error Overlay */}
                {media.error && (
                  <div className="absolute inset-0 bg-red-500/20 flex items-center justify-center">
                    <div className="text-center">
                      <AlertCircle className="h-6 w-6 text-red-500 mx-auto mb-1" />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => retryUpload(media.id)}
                        className="text-xs"
                      >
                        Retry
                      </Button>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Remove Button */}
              {!media.isUploading && (
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => removeMedia(media.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
              
              {/* Drag Handle for Reordering */}
              {!media.isUploading && !media.error && mediaFiles.length > 1 && (
                <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <GripVertical className="h-4 w-4 text-white drop-shadow-lg cursor-move" />
                </div>
              )}
              
              {/* File Info */}
              <div className="mt-2">
                <p className="text-xs text-muted-foreground truncate">
                  {media.filename}
                </p>
                {media.error && (
                  <p className="text-xs text-red-500 mt-1">
                    {media.error}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Global Upload Status */}
      {isUploading && (
        <Alert>
          <Upload className="h-4 w-4" />
          <AlertDescription>
            Uploading media files... Please wait.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
