import { CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface PostReviewStatusBadgeProps {
  status: 'pending' | 'approved' | 'rework';
  reviewCount?: {
    completed: number;
    total: number;
  };
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showProgress?: boolean;
  className?: string;
}

/**
 * Reusable badge component for displaying post review status.
 * Can show just status, or include progress information.
 */
export function PostReviewStatusBadge({
  status,
  reviewCount,
  size = 'md',
  showIcon = true,
  showProgress = false,
  className,
}: PostReviewStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'approved':
        return {
          label: 'Approved',
          icon: CheckCircle,
          className: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
        };
      case 'rework':
        return {
          label: 'Needs Rework',
          icon: AlertCircle,
          className: 'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200',
        };
      default:
        return {
          label: 'Pending Review',
          icon: Clock,
          className: 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200',
        };
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-1';
      case 'lg':
        return 'text-sm px-3 py-2';
      default:
        return 'text-xs px-2.5 py-1.5';
    }
  };

  const getIconSize = (size: string) => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3';
      case 'lg':
        return 'h-5 w-5';
      default:
        return 'h-4 w-4';
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  const formatProgress = () => {
    if (!reviewCount || !showProgress) return '';
    return ` (${reviewCount.completed}/${reviewCount.total})`;
  };

  return (
    <Badge
      variant="outline"
      className={cn(
        config.className,
        getSizeClasses(size),
        'inline-flex items-center gap-1.5 font-medium transition-colors',
        className
      )}
    >
      {showIcon && <Icon className={getIconSize(size)} />}
      <span>
        {config.label}
        {formatProgress()}
      </span>
    </Badge>
  );
}

/**
 * Compact version for use in post lists and cards.
 */
export function PostReviewStatusCompact({
  status,
  reviewCount,
  className,
}: {
  status: 'pending' | 'approved' | 'rework';
  reviewCount?: { completed: number; total: number };
  className?: string;
}) {
  return (
    <PostReviewStatusBadge
      status={status}
      reviewCount={reviewCount}
      size="sm"
      showProgress={!!reviewCount}
      className={className}
    />
  );
}

/**
 * Detailed version for use in post headers and review panels.
 */
export function PostReviewStatusDetailed({
  status,
  reviewCount,
  className,
}: {
  status: 'pending' | 'approved' | 'rework';
  reviewCount?: { completed: number; total: number };
  className?: string;
}) {
  const percentage = reviewCount 
    ? Math.round((reviewCount.completed / reviewCount.total) * 100)
    : 0;

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <PostReviewStatusBadge
        status={status}
        size="md"
        showIcon={true}
        className="flex-shrink-0"
      />
      {reviewCount && (
        <span className="text-sm text-muted-foreground">
          {percentage}% complete
        </span>
      )}
    </div>
  );
}
