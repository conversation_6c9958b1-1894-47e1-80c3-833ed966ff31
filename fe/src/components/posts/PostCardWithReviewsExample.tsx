import React from 'react';
import { PostCard } from './post-card';
import { PostsList } from './posts-list';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getPostPermissions, getReviewActions } from '@/lib/utils/post-permissions';
import type { components } from '@/lib/api/v1';

type PostListItemResponse = components['schemas']['PostListItemResponse'];

/**
 * Example component showing how to use the updated PostCard with review functionality.
 * This demonstrates the new simplified approach using server-side permission data.
 */

// Mock post data for demonstration
const mockPost: PostListItemResponse = {
  id: 1,
  caption: "Check out this amazing content! #collaboration #content",
  media_count: 2,
  media_uris: [
    {
      id: 1,
      type: 'image',
      url: 'https://example.com/image1.jpg',
      filename: 'image1.jpg',
      size: 1024000,
      created_at: '2024-01-15T10:00:00Z'
    },
    {
      id: 2,
      type: 'image', 
      url: 'https://example.com/image2.jpg',
      filename: 'image2.jpg',
      size: 2048000,
      created_at: '2024-01-15T10:01:00Z'
    }
  ],
  review_status: 'pending',
  my_review_status: 'pending',
  comment_count: 3,
  assigned_reviewer_count: 2,
  approved_by_count: 0,
  created_at: '2024-01-15T10:00:00Z',
  creator: {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>'
  },
  // Server-provided permission data
  can_edit: false,
  can_review: true,
  can_comment: true,
  is_assigned_reviewer: true,
  has_reviewer_role: true,
  user_role: 'reviewer'
};

/**
 * Example showing a single post card with review panel
 */
export function SinglePostWithReviews() {
  const permissions = getPostPermissions(mockPost);
  const actions = getReviewActions(mockPost);

  return (
    <div className="max-w-lg mx-auto space-y-6 p-6">
      <h2 className="text-xl font-bold">Single Post with Review Panel</h2>
      
      {/* Permission Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Current User Permissions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex flex-wrap gap-2">
            <Badge variant={permissions.canReview ? "default" : "secondary"}>
              Can Review: {permissions.canReview ? 'Yes' : 'No'}
            </Badge>
            <Badge variant={permissions.canEdit ? "default" : "secondary"}>
              Can Edit: {permissions.canEdit ? 'Yes' : 'No'}
            </Badge>
            <Badge variant={permissions.isAssignedReviewer ? "default" : "secondary"}>
              Assigned Reviewer: {permissions.isAssignedReviewer ? 'Yes' : 'No'}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            Role: {permissions.userRole} | Review Status: {permissions.myReviewStatus}
          </p>
        </CardContent>
      </Card>

      {/* Post Card with Review Panel */}
      <PostCard 
        post={mockPost} 
        showReviewPanel={true}
        onEdit={(postId) => console.log('Edit post:', postId)}
      />
    </div>
  );
}

/**
 * Example showing a list of posts with review panels
 */
export function PostsListWithReviews({ hubId }: { hubId: number }) {
  return (
    <div className="space-y-6 p-6">
      <h2 className="text-xl font-bold">Posts List with Review Panels</h2>
      
      <PostsList
        hubId={hubId}
        showReviewPanels={true}
        onEditPost={(postId) => console.log('Edit post:', postId)}
      />
    </div>
  );
}

/**
 * Example showing different user roles and their capabilities
 */
export function RoleBasedExamples() {
  const roles = [
    { role: 'admin', canReview: true, canEdit: true, isAssigned: false },
    { role: 'reviewer', canReview: true, canEdit: false, isAssigned: true },
    { role: 'content_creator', canReview: false, canEdit: true, isAssigned: false },
  ];

  return (
    <div className="space-y-6 p-6">
      <h2 className="text-xl font-bold">Role-Based Permission Examples</h2>
      
      {roles.map((roleExample) => {
        const examplePost: PostListItemResponse = {
          ...mockPost,
          can_edit: roleExample.canEdit,
          can_review: roleExample.canReview,
          is_assigned_reviewer: roleExample.isAssigned,
          has_reviewer_role: roleExample.canReview,
          user_role: roleExample.role,
          my_review_status: roleExample.isAssigned ? 'pending' : null,
        };

        const permissions = getPostPermissions(examplePost);
        const actions = getReviewActions(examplePost);

        return (
          <div key={roleExample.role} className="space-y-4">
            <h3 className="text-lg font-semibold capitalize">{roleExample.role} View</h3>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge variant="outline">Role: {roleExample.role}</Badge>
                  <Badge variant={actions.canSubmitReview ? "default" : "secondary"}>
                    Can Submit Review: {actions.canSubmitReview ? 'Yes' : 'No'}
                  </Badge>
                  <Badge variant={actions.showReviewPanel ? "default" : "secondary"}>
                    Show Review Panel: {actions.showReviewPanel ? 'Yes' : 'No'}
                  </Badge>
                </div>
                
                <PostCard 
                  post={examplePost}
                  showReviewPanel={actions.showReviewPanel}
                  className="border-0 shadow-none"
                />
              </CardContent>
            </Card>
          </div>
        );
      })}
    </div>
  );
}

/**
 * Main example component combining all demonstrations
 */
export function PostReviewExamples() {
  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold">Post Review System Examples</h1>
        <p className="text-muted-foreground">
          Demonstrating the new simplified permission system using server-side data
        </p>
      </div>

      <SinglePostWithReviews />
      <RoleBasedExamples />
      
      {/* Note about PostsList usage */}
      <Card>
        <CardHeader>
          <CardTitle>Using PostsList with Reviews</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            To use PostsList with review panels in your collaboration hub:
          </p>
          <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
{`<PostsList
  hubId={hubId}
  showReviewPanels={true}
  onEditPost={handleEditPost}
/>`}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
