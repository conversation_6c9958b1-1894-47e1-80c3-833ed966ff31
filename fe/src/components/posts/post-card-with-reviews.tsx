import React from 'react';
import { PostCard } from './post-card';
import { PostReviewPanel } from './PostReviewPanel';
import { PostReviewStatusBadge } from './PostReviewStatusBadge';
import { usePostReviewStats } from '@/lib/api/hooks/usePostReviews';
import { usePostReviewPermissions } from '@/lib/api/hooks/usePostReviewPermissions';
import type { PostListItemResponse } from '@/lib/types/api';

interface PostCardWithReviewsProps {
  post: PostListItemResponse;
  onEdit?: (postId: number) => void;
  hubId?: number;
  showReviewPanel?: boolean;
  className?: string;
}

/**
 * Enhanced post card that includes review functionality.
 * Extends the existing PostCard with review panel and status indicators.
 * Automatically determines review permissions based on user role and assignments.
 */
export function PostCardWithReviews({
  post,
  onEdit,
  hubId,
  showReviewPanel = true,
  className
}: PostCardWithReviewsProps) {
  const _reviewStats = usePostReviewStats(post.id);
  const permissions = usePostReviewPermissions(post.id, hubId);

  return (
    <div className="space-y-3">
      {/* Original Post Card */}
      <PostCard
        post={post}
        onEdit={onEdit}
        className={className}
      />

      {/* Review Panel - only show if user can view reviews */}
      {showReviewPanel && permissions.canViewReviews && (
        <PostReviewPanel
          postId={post.id}
          hubId={hubId}
          className="ml-0"
        />
      )}
    </div>
  );
}

/**
 * Compact version for post lists where space is limited.
 * Shows only review status badge without the full panel.
 */
export function PostCardCompact({
  post,
  onEdit,
  hubId,
  className
}: PostCardWithReviewsProps) {
  const reviewStats = usePostReviewStats(post.id);
  const permissions = usePostReviewPermissions(post.id, hubId);

  return (
    <div className="space-y-2">
      <PostCard
        post={post}
        onEdit={onEdit}
        className={className}
      />

      {/* Compact Review Status - show if user can view reviews */}
      {permissions.canViewReviews && (
        <div className="px-4 pb-2 flex items-center justify-between">
          <PostReviewStatusBadge
            status={reviewStats.overallStatus}
            reviewCount={{
              completed: reviewStats.approvedReviews + reviewStats.reworkReviews,
              total: reviewStats.totalReviewers,
            }}
            size="sm"
            showProgress={true}
          />

          {/* Show user's review status if they're a reviewer */}
          {permissions.isAssignedReviewer && (
            <span className="text-xs text-muted-foreground">
              {permissions.myReviewStatus === 'approved' && '✓ You approved'}
              {permissions.myReviewStatus === 'rework' && '⚠ You requested rework'}
              {permissions.myReviewStatus === 'pending' && '⏳ Your review pending'}
              {permissions.myReviewStatus === 'assigned' && '📝 Review assigned'}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
