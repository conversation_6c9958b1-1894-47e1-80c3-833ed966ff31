import React, { useState } from 'react';
import { Ch<PERSON>ronDown, ChevronUp, CheckCircle, AlertCircle, Clock, User, Shield, UserCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PostReviewForm } from './post-review-form';
import { usePostReviews, usePostReviewStats } from '@/lib/api/hooks/usePostReviews';
import { getPostPermissions, getReviewActions } from '@/lib/utils/post-permissions';
import type { PostReviewResponse, components } from '@/lib/types/api';

type PostListItemResponse = components['schemas']['PostListItemResponse'];
type PostResponse = components['schemas']['PostResponse'];

interface PostReviewPanelProps {
  post: PostListItemResponse | PostResponse;
  className?: string;
}

/**
 * Inline review panel that appears below posts in the collaboration hub.
 * Shows review status, allows reviewers to submit reviews, and displays other reviews.
 * Uses permission data already included in the post response.
 */
export function PostReviewPanel({ post, className }: PostReviewPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);

  const postId = post.id!;
  const { data: reviewsData, isLoading: reviewsLoading } = usePostReviews(postId);
  const reviewStats = usePostReviewStats(postId);
  const permissions = getPostPermissions(post);
  const actions = getReviewActions(post);

  const handleReviewSubmitted = () => {
    setShowReviewForm(false);
    setIsExpanded(true); // Keep panel expanded to show updated reviews
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rework':
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rework':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatReviewerName = (review: PostReviewResponse) => {
    return review.reviewer_name || review.reviewer_email;
  };

  const getReviewerInitials = (review: PostReviewResponse) => {
    const name = review.reviewer_name || review.reviewer_email;
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleIcon = (review: PostReviewResponse) => {
    // Note: We can't easily determine "You" without additional user context
    // This could be enhanced by passing current user info to the component
    if (review.is_external) {
      return <User className="h-3 w-3 text-gray-500" title="External Reviewer" />;
    }
    return <Shield className="h-3 w-3 text-green-600" title="Internal Reviewer" />;
  };

  if (reviewsLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(reviewStats.overallStatus)}
                <div>
                  <CardTitle className="text-sm font-medium">
                    Review Status: {reviewStats.overallStatus === 'pending' ? 'Pending' : 
                                   reviewStats.overallStatus === 'approved' ? 'Approved' : 'Needs Rework'}
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">
                    {reviewStats.approvedReviews + reviewStats.reworkReviews} of {reviewStats.totalReviewers} reviewers completed
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge 
                  variant="outline" 
                  className={getStatusColor(reviewStats.overallStatus)}
                >
                  {reviewStats.completionPercentage}% Complete
                </Badge>
                {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0 pb-4 px-4">
            {/* Permission Status Alert */}
            {!permissions.canReview && permissions.userRole && (
              <Alert className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {permissions.isAssignedReviewer
                    ? `You have ${permissions.userRole} role but are not assigned as a reviewer for this post.`
                    : permissions.hasReviewerRole
                    ? "You are not assigned as a reviewer for this post."
                    : `Your role (${permissions.userRole}) cannot review posts. Only admins, reviewers, and reviewer-creators can review.`
                  }
                </AlertDescription>
              </Alert>
            )}

            {/* Review Form Section */}
            {actions.showReviewForm && (
              <>
                {!showReviewForm ? (
                  <div className="mb-4">
                    <Button
                      onClick={() => setShowReviewForm(true)}
                      variant={permissions.hasExistingReview ? "outline" : "default"}
                      size="sm"
                      className="w-full"
                      disabled={!actions.canSubmitReview}
                    >
                      {actions.reviewButtonText}
                    </Button>
                    {permissions.myReviewStatus !== 'not_assigned' && (
                      <p className="text-xs text-muted-foreground text-center mt-1">
                        {actions.reviewStatusText}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="mb-4">
                    <PostReviewForm
                      postId={postId}
                      existingReview={permissions.myReview}
                      onSubmitted={handleReviewSubmitted}
                      onCancel={() => setShowReviewForm(false)}
                    />
                  </div>
                )}
                <Separator className="mb-4" />
              </>
            )}

            {/* Reviews List */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-muted-foreground">
                All Reviews ({reviewsData?.reviews.length || 0})
              </h4>
              
              {reviewsData?.reviews.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No reviews submitted yet
                </p>
              ) : (
                <div className="space-y-3">
                  {reviewsData?.reviews.map((review) => (
                    <div key={review.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="text-xs">
                          {getReviewerInitials(review)}
                        </AvatarFallback>
                      </Avatar>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium">
                            {formatReviewerName(review)}
                          </span>
                          {getRoleIcon(review)}
                          {review.is_external && (
                            <Badge variant="secondary" className="text-xs">
                              External
                            </Badge>
                          )}
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(review.decision)}
                            <span className="text-xs text-muted-foreground capitalize">
                              {review.decision}
                            </span>
                          </div>
                        </div>
                        
                        {review.comment && (
                          <p className="text-sm text-gray-700 break-words">
                            {review.comment}
                          </p>
                        )}
                        
                        <p className="text-xs text-muted-foreground mt-1">
                          {review.reviewed_at ? 
                            `Reviewed ${new Date(review.reviewed_at).toLocaleDateString()}` :
                            `Assigned ${new Date(review.assigned_at).toLocaleDateString()}`
                          }
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
