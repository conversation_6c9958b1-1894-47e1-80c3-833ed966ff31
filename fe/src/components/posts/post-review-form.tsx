import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useCreateOrUpdatePostReview } from '@/lib/api/hooks/usePostReviews';
import type { PostReviewResponse, ReviewStatus } from '@/lib/types/api';

// Form validation schema
const reviewFormSchema = z.object({
  decision: z.enum(['approved', 'rework'], {
    required_error: 'Please select a review decision',
  }),
  comment: z
    .string()
    .min(1, 'Review comment is required')
    .max(2000, 'Comment cannot exceed 2000 characters'),
});

type ReviewFormData = z.infer<typeof reviewFormSchema>;

interface PostReviewFormProps {
  postId: number;
  existingReview?: PostReviewResponse | null;
  onSubmitted?: () => void;
  onCancel?: () => void;
}

/**
 * Form component for creating or updating post reviews.
 * Supports both new reviews and editing existing reviews (upsert behavior).
 */
export function PostReviewForm({ 
  postId, 
  existingReview, 
  onSubmitted, 
  onCancel 
}: PostReviewFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createOrUpdateReview = useCreateOrUpdatePostReview();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    reset,
  } = useForm<ReviewFormData>({
    resolver: zodResolver(reviewFormSchema),
    defaultValues: {
      decision: existingReview?.decision as ReviewStatus || undefined,
      comment: existingReview?.comment || '',
    },
    mode: 'onChange',
  });

  const watchedComment = watch('comment');
  const watchedDecision = watch('decision');
  const commentLength = watchedComment?.length || 0;

  // Update form when existing review changes
  useEffect(() => {
    if (existingReview) {
      setValue('decision', existingReview.decision as ReviewStatus);
      setValue('comment', existingReview.comment);
    }
  }, [existingReview, setValue]);

  const onSubmit = async (data: ReviewFormData) => {
    setIsSubmitting(true);
    
    try {
      await createOrUpdateReview.mutateAsync({
        postId,
        review: {
          decision: data.decision as ReviewStatus,
          comment: data.comment,
        },
      });

      // Reset form and notify parent
      reset();
      onSubmitted?.();
    } catch (error) {
      console.error('Failed to submit review:', error);
      // Error handling is managed by the mutation hook
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    reset();
    onCancel?.();
  };

  return (
    <Card className="border-dashed">
      <CardContent className="p-4">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Decision Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Your Review Decision</Label>
            <RadioGroup
              value={watchedDecision}
              onValueChange={(value) => setValue('decision', value as ReviewStatus)}
              className="grid grid-cols-1 md:grid-cols-2 gap-3"
            >
              {/* Approve Option */}
              <Label
                htmlFor="approved"
                className={`flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                  watchedDecision === 'approved'
                    ? 'bg-green-50 border-green-200 ring-2 ring-green-500 ring-opacity-20'
                    : 'hover:bg-green-50 hover:border-green-200'
                }`}
              >
                <RadioGroupItem value="approved" id="approved" />
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium text-green-800">Approve</div>
                  <div className="text-xs text-green-600">Ready to publish</div>
                </div>
              </Label>

              {/* Rework Option */}
              <Label
                htmlFor="rework"
                className={`flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                  watchedDecision === 'rework'
                    ? 'bg-orange-50 border-orange-200 ring-2 ring-orange-500 ring-opacity-20'
                    : 'hover:bg-orange-50 hover:border-orange-200'
                }`}
              >
                <RadioGroupItem value="rework" id="rework" />
                <AlertCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="font-medium text-orange-800">Request Rework</div>
                  <div className="text-xs text-orange-600">Needs changes</div>
                </div>
              </Label>
            </RadioGroup>
            {errors.decision && (
              <p className="text-sm text-red-600">{errors.decision.message}</p>
            )}
          </div>

          {/* Comment Field */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-sm font-medium">
              Review Comments <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="comment"
              placeholder={
                watchedDecision === 'approved'
                  ? 'Provide positive feedback and any final notes...'
                  : watchedDecision === 'rework'
                  ? 'Explain what needs to be changed and provide specific feedback...'
                  : 'Provide specific feedback for your review decision...'
              }
              className="min-h-[100px] resize-none"
              maxLength={2000}
              {...register('comment')}
            />
            <div className="flex justify-between items-center">
              <div>
                {errors.comment && (
                  <p className="text-sm text-red-600">{errors.comment.message}</p>
                )}
              </div>
              <p className={`text-xs ${commentLength > 1800 ? 'text-orange-600' : 'text-muted-foreground'}`}>
                {commentLength} / 2,000 characters
              </p>
            </div>
          </div>

          {/* Error Display */}
          {createOrUpdateReview.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to submit review. Please try again.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : existingReview ? (
                'Update Review'
              ) : (
                'Submit Review'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
