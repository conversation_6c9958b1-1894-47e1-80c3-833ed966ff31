/**
 * Re-exports of OpenAPI-generated types for Posts components.
 *
 * Components should use these OpenAPI types directly for consistency with the backend API.
 * For component-specific form types, define them locally using zod schemas for better validation.
 * For complex UI state, define types within the component files where they're used.
 */

// Re-export OpenAPI generated types from decoupled API types
export type {
  PostResponse,
  PostListItemResponse,
  PostCreateRequest,
  PostUpdateRequest,
  PostCreator,
  MediaItem,
  PostListResponse,
  PostFilters,
  FileValidationResponse,
  ValidationDetails,
  // Post reviewer types
  PostReviewer,
  // Enums
  PostFiltersAvailableStatuses,
  PostFiltersCurrentStatus,
  PostListItemResponseReview_status,
  PostListItemResponseMy_review_status,
} from '@/lib/types/api';

// Additional component-specific types
export interface MediaFile {
  id: string;
  file?: File;
  url: string;
  type: 'image' | 'video';
  filename: string;
  size: number;
  isUploading?: boolean;
  uploadProgress?: number;
  error?: string;
}

export interface PostFormData {
  caption?: string;
  media_uris?: string[];
  reviewer_notes?: string;
  reviewer_ids?: number[];
}
