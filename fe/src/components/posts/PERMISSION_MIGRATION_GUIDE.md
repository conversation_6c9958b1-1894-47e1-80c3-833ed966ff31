# Post Permission System Migration Guide

## Overview

We've optimized the post permission system by moving permission calculations from the frontend to the backend. This eliminates multiple API calls and improves performance.

## What Changed

### Before (Old Approach)
```typescript
// ❌ OLD: Multiple API calls for permissions
import { usePostReviewPermissions, useReviewActions } from '@/lib/api/hooks/usePostReviewPermissions';

function PostComponent({ postId, hubId }) {
  const permissions = usePostReviewPermissions(postId, hubId); // 3 API calls!
  const actions = useReviewActions(postId, hubId);

  // Uses: useMyPostReview + usePostReviews + hub participant query
}
```

### After (New Approach)
```typescript
// ✅ NEW: Use utility functions with post data
import { getPostPermissions, getReviewActions } from '@/lib/utils/post-permissions';
import { usePost } from '@/hooks/posts/use-post';

function PostComponent({ postId }) {
  const { data: post } = usePost(postId); // 1 API call
  const permissions = getPostPermissions(post); // No additional API calls!
  const actions = getReviewActions(post);
}
```

## Migration Steps

### 1. Update Imports
```typescript
// Replace this:
import { usePostReviewPermissions, useReviewActions, useReviewerRoleInfo } from '@/lib/api/hooks/usePostReviewPermissions';

// With this:
import { getPostPermissions, getReviewActions, getReviewerRoleInfo } from '@/lib/utils/post-permissions';
```

### 2. Update Hook Usage

#### For Single Posts
```typescript
// OLD
function PostDetail({ postId, hubId }) {
  const permissions = usePostReviewPermissions(postId, hubId);
  const actions = useReviewActions(postId, hubId);

  // Component logic...
}

// NEW
function PostDetail({ postId }) {
  const { data: post } = usePost(postId);
  const permissions = getPostPermissions(post);
  const actions = getReviewActions(post);

  // Component logic...
}
```

#### For Post Lists
```typescript
// OLD
function PostList({ posts, hubId }) {
  return posts.map(post => (
    <PostCard
      key={post.id}
      post={post}
      permissions={usePostReviewPermissions(post.id, hubId)} // ❌ Hook in loop!
    />
  ));
}

// NEW - Using PostCard directly
function PostList({ posts }) {
  return posts.map(post => (
    <PostCard
      key={post.id}
      post={post}
      showReviewPanel={true} // ✅ Permissions calculated inside PostCard
    />
  ));
}

// NEW - Using PostsList component
function PostsContainer({ hubId }) {
  return (
    <PostsList
      hubId={hubId}
      showReviewPanels={true}
      onEditPost={handleEditPost}
    />
  );
}
```

### 3. Update Component Props

#### PostCard Components
```typescript
// OLD
interface PostCardProps {
  post: PostListItemResponse;
  hubId: number;
}

function PostCard({ post, hubId }: PostCardProps) {
  const permissions = usePostReviewPermissions(post.id, hubId);
  // ...
}

// NEW
interface PostCardProps {
  post: PostListItemResponse;
}

function PostCard({ post }: PostCardProps) {
  const permissions = usePostPermissions(post);
  // ...
}
```

### 4. Available Permission Data

The new system provides the same permission data, but sourced from the backend:

```typescript
const permissions = getPostPermissions(post);

// All these properties are still available:
permissions.canReview          // boolean
permissions.canEdit            // boolean
permissions.canComment         // boolean
permissions.canAssignReviewers // boolean
permissions.hasReviewerRole    // boolean
permissions.isAssignedReviewer // boolean
permissions.myReviewStatus     // 'approved' | 'rework' | 'pending' | 'not_assigned'
permissions.userRole           // 'admin' | 'reviewer' | 'reviewer_creator' | 'content_creator'
permissions.totalReviewers     // number
permissions.completedReviews   // number
permissions.pendingReviews     // number
```

## Backend Changes

The backend now includes comprehensive permission data in post responses:

### PostResponse (Single Post)
```json
{
  "id": 123,
  "caption": "...",
  "permissions": {
    "can_edit": true,
    "can_review": false,
    "can_comment": true,
    "can_assign_reviewers": false,
    "is_assigned_reviewer": false,
    "has_reviewer_role": false,
    "my_review_status": "not_assigned",
    "user_role": "content_creator"
  }
}
```

### PostListItemResponse (Post Lists)
```json
{
  "id": 123,
  "caption": "...",
  "can_edit": true,
  "can_review": false,
  "can_comment": true,
  "is_assigned_reviewer": false,
  "has_reviewer_role": false,
  "my_review_status": "not_assigned",
  "user_role": "content_creator"
}
```

## Performance Benefits

- **Before**: 3 API calls per post for permissions
- **After**: 0 additional API calls (data included in post response)
- **Result**: Faster loading, reduced server load, better UX

## Files to Update

1. **Components using old hooks:**
   - `PostReviewPanel.tsx`
   - `PostCardWithReviews.tsx`
   - `PostReviewIntegrationExample.tsx`
   - Any custom components using `usePostReviewPermissions`

2. **New files added:**
   - `fe/src/lib/utils/post-permissions.ts` (utility functions)
   - `fe/src/components/posts/PostCardWithReviewsExample.tsx` (examples)

3. **Files removed:**
   - `fe/src/lib/api/hooks/usePostReviewPermissions.ts` (deleted)
   - `fe/src/lib/api/hooks/usePostPermissions.ts` (deleted)

4. **Update imports in:**
   - All components importing from the old hook files

## Testing

After migration, verify:
1. Permission checks still work correctly
2. Review buttons show/hide appropriately
3. Role-based access is maintained
4. No additional API calls are made for permissions
5. Loading states work properly
