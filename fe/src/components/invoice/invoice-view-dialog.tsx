import { FileText, Download, Loader2, AlertCircle, Building, CreditCard, User } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useInvoice, useInvoicePdf } from '@/hooks/invoices';
import { useAccountCompany } from '@/hooks/account-companies';
import { useBrand } from '@/hooks/brands';
import { useBankDetail } from '@/hooks/bank-details';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { INVOICE_STATUS_CONFIG } from './types';
import type { InvoiceDisplayData } from './types';

interface InvoiceViewDialogProps {
  invoice: InvoiceDisplayData | null;
  open: boolean;
  onClose: () => void;
}

export function InvoiceViewDialog({ 
  invoice, 
  open, 
  onClose 
}: InvoiceViewDialogProps) {
  const { t, keys } = useTranslations();
  const { downloadPdf } = useInvoicePdf();

  // Fetch full invoice details when dialog opens
  const {
    data: invoiceData,
    isLoading,
    error,
    isError
  } = useInvoice(invoice?.id || null);

  // Fetch related entities when invoice data is available
  const {
    data: issuerData,
    isLoading: isLoadingIssuer
  } = useAccountCompany(invoiceData?.issuer_id, !!invoiceData?.issuer_id);

  const {
    data: recipientData,
    isLoading: isLoadingRecipient
  } = useBrand(invoiceData?.recipient_id || 0, !!invoiceData?.recipient_id);

  const {
    data: bankDetailsData,
    isLoading: isLoadingBankDetails
  } = useBankDetail(invoiceData?.bank_details_id || 0, !!invoiceData?.bank_details_id);

  const handleDownload = async () => {
    if (!invoice) return;
    
    try {
      await downloadPdf({
        id: invoice.id
      });
    } catch (error) {
      console.error('Failed to download PDF:', error);
    }
  };

  if (!invoice) return null;

  const statusConfig = INVOICE_STATUS_CONFIG[invoice.status];
  const isLoadingAnyData = isLoading || isLoadingIssuer || isLoadingRecipient || isLoadingBankDetails;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] w-full overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-primary" />
            <div>
              <DialogTitle className="text-xl">
                {t(keys.invoices.viewDialog.title)}
              </DialogTitle>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-lg font-semibold">{invoice.invoice_number}</span>
                <Badge variant={statusConfig.color}>
                  {statusConfig.label}
                </Badge>
              </div>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            {t(keys.invoices.viewDialog.downloadPdf)}
          </Button>
        </DialogHeader>

        {isLoadingAnyData && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>{t(keys.invoices.viewDialog.loading)}</span>
            </div>
          </div>
        )}

        {isError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error?.error?.message || t(keys.invoices.viewDialog.error)}
            </AlertDescription>
          </Alert>
        )}

        {invoiceData && (
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t(keys.invoices.viewDialog.basicInfo)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t(keys.invoices.invoiceNumber)}
                    </label>
                    <p className="text-sm font-semibold">{invoiceData.invoice_number}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t(keys.invoices.form.currency)}
                    </label>
                    <p className="text-sm">{invoiceData.currency}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t(keys.invoices.issueDate)}
                    </label>
                    <p className="text-sm">
                      {new Date(invoiceData.issue_date).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t(keys.invoices.dueDate)}
                    </label>
                    <p className="text-sm">
                      {new Date(invoiceData.due_date).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Template
                    </label>
                    <p className="text-sm">{invoiceData.template}</p>
                  </div>
                  {invoiceData.days_until_due !== undefined && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Days Until Due
                      </label>
                      <p className="text-sm">
                        {invoiceData.is_overdue
                          ? `${Math.abs(invoiceData.days_until_due)} days overdue`
                          : `${invoiceData.days_until_due} days`
                        }
                      </p>
                    </div>
                  )}
                </div>
                {invoiceData.notes && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t(keys.invoices.form.notes)}
                    </label>
                    <p className="text-sm whitespace-pre-wrap">{invoiceData.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Issuer Information */}
            {issuerData && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    {t(keys.invoices.viewDialog.issuerInfo)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t(keys.invoices.viewDialog.companyName)}
                      </label>
                      <p className="text-sm font-semibold">{issuerData.company_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Email
                      </label>
                      <p className="text-sm">{issuerData.email || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Phone
                      </label>
                      <p className="text-sm">{issuerData.phone || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Website
                      </label>
                      <p className="text-sm">{issuerData.website || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        VAT Number
                      </label>
                      <p className="text-sm">{issuerData.vat_number || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Registration Number
                      </label>
                      <p className="text-sm">{issuerData.registration_number || '—'}</p>
                    </div>
                  </div>
                  {(issuerData.address_street || issuerData.address_city || issuerData.address_postal_code || issuerData.address_country) && (
                    <>
                      <Separator className="my-4" />
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          {t(keys.invoices.viewDialog.address)}
                        </label>
                        <div className="text-sm space-y-1">
                          {issuerData.address_street && <p>{issuerData.address_street}</p>}
                          <p>
                            {[issuerData.address_postal_code, issuerData.address_city].filter(Boolean).join(' ')}
                          </p>
                          {issuerData.address_country && <p>{issuerData.address_country}</p>}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Recipient Information */}
            {recipientData && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <User className="h-5 w-5" />
                    {t(keys.invoices.viewDialog.recipientInfo)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t(keys.invoices.viewDialog.name)}
                      </label>
                      <p className="text-sm font-semibold">{recipientData.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t(keys.invoices.viewDialog.companyName)}
                      </label>
                      <p className="text-sm font-semibold">{recipientData.company_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Email
                      </label>
                      <p className="text-sm">{recipientData.email || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Phone
                      </label>
                      <p className="text-sm">{recipientData.phone || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Website
                      </label>
                      <p className="text-sm">{recipientData.website || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        VAT Number
                      </label>
                      <p className="text-sm">{recipientData.vat_number || '—'}</p>
                    </div>
                  </div>
                  {(recipientData.address_street || recipientData.address_city || recipientData.address_postal_code || recipientData.address_country) && (
                    <>
                      <Separator className="my-4" />
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          {t(keys.invoices.viewDialog.address)}
                        </label>
                        <div className="text-sm space-y-1">
                          {recipientData.address_street && <p>{recipientData.address_street}</p>}
                          <p>
                            {[recipientData.address_postal_code, recipientData.address_city].filter(Boolean).join(' ')}
                          </p>
                          {recipientData.address_country && <p>{recipientData.address_country}</p>}
                        </div>
                      </div>
                    </>
                  )}
                  {recipientData.contacts && recipientData.contacts.length > 0 && (
                    <>
                      <Separator className="my-4" />
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          {t(keys.invoices.viewDialog.contacts)}
                        </label>
                        <div className="space-y-2 mt-2">
                          {recipientData.contacts.map((contact) => (
                            <div key={contact.id} className="flex items-center justify-between p-2 border rounded">
                              <div>
                                <p className="text-sm font-medium">{contact.name}</p>
                                <p className="text-xs text-muted-foreground">{contact.email}</p>
                              </div>
                              {contact.notes && (
                                <p className="text-xs text-muted-foreground max-w-xs truncate">
                                  {contact.notes}
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Bank Details Information */}
            {bankDetailsData && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    {t(keys.invoices.viewDialog.bankDetailsInfo)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t(keys.invoices.viewDialog.accountName)}
                      </label>
                      <p className="text-sm font-semibold">{bankDetailsData.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t(keys.invoices.viewDialog.bankName)}
                      </label>
                      <p className="text-sm">{bankDetailsData.bank_name || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        IBAN
                      </label>
                      <p className="text-sm font-mono">{bankDetailsData.iban || '—'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        BIC/SWIFT
                      </label>
                      <p className="text-sm font-mono">{bankDetailsData.bic_swift || '—'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Line Items */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t(keys.invoices.viewDialog.lineItems)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {invoiceData.items && invoiceData.items.length > 0 ? (
                  <div className="space-y-4">
                    {invoiceData.items.map((item) => (
                      <div key={item.id} className="border rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                          <div className="lg:col-span-2">
                            <label className="text-sm font-medium text-muted-foreground">
                              {t(keys.invoices.form.description)}
                            </label>
                            <p className="text-sm">{item.description}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              {t(keys.invoices.form.quantity)}
                            </label>
                            <p className="text-sm">{Number(item.quantity)}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              {t(keys.invoices.form.unitPrice)}
                            </label>
                            <p className="text-sm">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: invoiceData.currency,
                              }).format(Number(item.unit_price))}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              {t(keys.invoices.form.vatRate)}
                            </label>
                            <p className="text-sm">{Number(item.vat_rate)}%</p>
                          </div>
                        </div>
                        <Separator className="my-3" />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              {t(keys.invoices.form.lineTotal)}
                            </label>
                            <p className="text-sm font-semibold">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: invoiceData.currency,
                              }).format(Number(item.line_total))}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">
                              {t(keys.invoices.viewDialog.vatAmount)}
                            </label>
                            <p className="text-sm">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: invoiceData.currency,
                              }).format(Number(item.vat_amount))}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-8">
                    {t(keys.invoices.viewDialog.noLineItems)}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Recipients */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t(keys.invoices.viewDialog.recipients)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {invoiceData.recipients && invoiceData.recipients.length > 0 ? (
                  <div className="space-y-3">
                    {invoiceData.recipients.map((recipient) => {
                      const typeLabel = recipient.type === 'original'
                        ? t(keys.invoices.viewDialog.recipientLabels.type.original)
                        : t(keys.invoices.viewDialog.recipientLabels.type.copy);

                      const sourceLabel = recipient.source === 'brand_contact'
                        ? t(keys.invoices.viewDialog.recipientLabels.source.brand_contact)
                        : t(keys.invoices.viewDialog.recipientLabels.source.manual);

                      const sendCountText = t(keys.invoices.viewDialog.recipientLabels.sendCount, {
                        count: recipient.send_count,
                        plural: recipient.send_count !== 1 ? 's' : ''
                      });

                      return (
                        <div key={recipient.id} className="flex items-start justify-between p-4 border rounded-lg bg-muted/20">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <p className="text-sm font-medium truncate">{recipient.email}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={recipient.type === 'original' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {typeLabel}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {sourceLabel}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right text-xs text-muted-foreground ml-4 flex-shrink-0">
                            <p className="mb-1">{sendCountText}</p>
                            {recipient.last_sent_at && (
                              <p>
                                {t(keys.invoices.viewDialog.recipientLabels.lastSent)}: {new Date(recipient.last_sent_at).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-8">
                    {t(keys.invoices.viewDialog.recipientLabels.noRecipients)}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Totals */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t(keys.invoices.viewDialog.totals)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t(keys.invoices.subtotal)}
                    </span>
                    <span className="text-sm font-medium">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: invoiceData.currency,
                      }).format(Number(invoiceData.subtotal))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t(keys.invoices.totalVat)}
                    </span>
                    <span className="text-sm font-medium">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: invoiceData.currency,
                      }).format(Number(invoiceData.total_vat))}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="text-base font-semibold">
                      {t(keys.invoices.totalAmount)}
                    </span>
                    <span className="text-base font-bold">
                      {new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: invoiceData.currency,
                      }).format(Number(invoiceData.total_amount))}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
