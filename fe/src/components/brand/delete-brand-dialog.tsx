import { Alert<PERSON>riangle } from "lucide-react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useDeleteBrand } from "@/hooks/brands"
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { Brand } from './types'

interface DeleteBrandDialogProps {
  brand: Brand | null
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export function DeleteBrandDialog({ brand, open, onClose, onSuccess }: DeleteBrandDialogProps) {
  const { t, keys } = useTranslations()
  const deleteBrand = useDeleteBrand()

  const handleDelete = async () => {
    if (!brand) return

    try {
      await deleteBrand.mutateAsync({
        params: { path: { id: brand.id } }
      })
      
      onSuccess()
      onClose()
    } catch (error) {
      // Error handling is managed by the mutation hook and will show appropriate error messages
      console.error('Failed to delete brand:', error)
    }
  }

  if (!brand) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="w-[calc(100vw-1rem)] max-w-[425px] mx-2 sm:mx-auto">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-destructive/10 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <div>
              <DialogTitle>{t(keys.brands.confirmDelete)}</DialogTitle>
              <DialogDescription className="mt-1">
                {t(keys.brands.deleteDescription)}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="space-y-1">
              <p className="font-medium">{brand.name}</p>
              <p className="text-sm text-muted-foreground">{brand.company_name}</p>
              {brand.contacts.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  {brand.contacts.length === 1
                    ? `${brand.contacts.length} contact will also be deleted`
                    : `${brand.contacts.length} contacts will also be deleted`
                  }
                </p>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={deleteBrand.isPending}
            className="min-h-[44px]"
          >
            {t(keys.brands.cancel)}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteBrand.isPending}
            className="min-h-[44px]"
          >
            {deleteBrand.isPending
              ? t(keys.brands.deleting)
              : t(keys.brands.delete)
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
