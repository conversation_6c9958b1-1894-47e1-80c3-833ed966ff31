import { Tag, Mail, Phone, MapPin, Globe, MoreHorizontal, Pencil, Trash2 } from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { BrandDisplayData } from './types';

interface BrandCardProps {
  brand: BrandDisplayData
  onEdit: () => void
  onDelete: () => void
}

export function BrandCard({ brand, onEdit, onDelete }: BrandCardProps) {
  const { t, keys } = useTranslations();

  return (
    <Card className="w-full hover:shadow-lg transition-shadow duration-200 relative">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Tag className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-lg leading-tight">{brand.name}</h3>
              <p className="text-sm text-muted-foreground mt-1">{brand.company_name}</p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={onEdit}>
                <Pencil className="h-4 w-4 mr-2" />
                {t(keys.brands.edit)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onDelete}
                className="text-destructive focus:text-destructive focus:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {t(keys.brands.delete)}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Contact Information */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Mail className="h-4 w-4" />
            {brand.primary_contact_email || brand.email ? (
              <a 
                href={`mailto:${brand.primary_contact_email || brand.email}`} 
                className="hover:text-primary transition-colors"
              >
                {brand.primary_contact_email || brand.email}
              </a>
            ) : (
              <span>—</span>
            )}
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-4 w-4" />
            {brand.phone ? (
              <a href={`tel:${brand.phone}`} className="hover:text-primary transition-colors">
                {brand.phone}
              </a>
            ) : (
              <span>—</span>
            )}
          </div>
        </div>

        {/* Location */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <MapPin className="h-4 w-4" />
          <span>
            {brand.address_city || brand.address_country
              ? [brand.address_city, brand.address_country].filter(Boolean).join(", ")
              : "—"
            }
          </span>
        </div>

        {/* Website */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Globe className="h-4 w-4" />
          {brand.website ? (
            <a
              href={brand.website}
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary transition-colors truncate"
            >
              {brand.website.replace(/^https?:\/\//, "")}
            </a>
          ) : (
            <span>—</span>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
