import * as React from "react"
import { useLocation, useNavigate } from 'react-router';

import {
  Sidebar,
  SidebarContent, SidebarGroup, SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import { GalleryVerticalEnd, LayoutDashboard, Building2, CreditCard, Tag, FileText, Users } from 'lucide-react'
import { ROUTES } from "@/router/routes"

// Flat navigation structure using route constants
const navItems = [
  {
    title: "Dashboard",
    url: ROUTES.DASHBOARD,
    icon: LayoutDashboard
  },
  {
    title: "Account companies",
    url: ROUTES.ACCOUNT_COMPANIES,
    icon: Building2
  },
  {
    title: "Bank Details",
    url: ROUTES.BANK_DETAILS,
    icon: CreditCard
  },
  {
    title: "Brands",
    url: ROUTES.BRANDS,
    icon: Tag
  },
  {
    title: "Hu<PERSON>",
    url: ROUTES.COLLABORATION_HUBS,
    icon: Users
  },
  {
    title: "Invoices",
    url: ROUTES.INVOICES,
    icon: FileText
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const location = useLocation()
  const navigate = useNavigate()

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <GalleryVerticalEnd className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none">
                <span className="font-medium">Collaboration Hub</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent className="flex flex-col gap-2">
            <SidebarMenu>
              {navItems.map((item) => {
                const isActive = location.pathname === item.url
                return (
                  <SidebarMenuItem key={item.title} onClick={() => navigate(item.url)}>
                    <SidebarMenuButton isActive={isActive}>
                      {item.icon && <item.icon />}
                      {item.title}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  )
}
