import { useForm } from "react-hook-form"
import * as yup from "yup"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "@/components/ui/dialog.tsx"
import { Button } from "@/components/ui/button.tsx"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form.tsx"
import { Input } from "@/components/ui/input.tsx"
import { Alert, AlertDescription } from "@/components/ui/alert.tsx"
import { AlertCircle } from "lucide-react"
import {
  useCreateAccountCompany,
  useUpdateAccountCompany,
} from "@/hooks/account-companies"
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useEffect, useMemo } from "react"
import { yupResolver } from '@hookform/resolvers/yup';
import type { AccountCompany } from '@/components/account-company/types';

type CompanyFormValues = {
  company_name: string;
  email?: string;
  phone?: string;
  website?: string;
  address_street?: string;
  address_city?: string;
  address_postal_code?: string;
  address_country?: string;
  vat_number?: string;
  registration_number?: string;
}

interface EditCompanyDialogProps {
  company: AccountCompany | null
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function EditCompanyDialog({ company, open, onClose, onSuccess }: EditCompanyDialogProps) {
  const isEditing = !!company
  const { mutate: createCompany, isPending: isCreating, error: createError, isError: isCreateError } = useCreateAccountCompany()
  const { mutate: updateCompany, isPending: isUpdating, error: updateError, isError: isUpdateError } = useUpdateAccountCompany()
  const { t, keys } = useTranslations()

  const schema: yup.ObjectSchema<CompanyFormValues> = useMemo(() => yup.object({
    company_name: yup
      .string()
      .required(t(keys.accountCompanies.validation.companyNameRequired))
      .max(255, t(keys.accountCompanies.validation.companyNameTooLong)),
    email: yup
      .string()
      .email(t(keys.accountCompanies.validation.emailInvalid))
      .max(255, t(keys.accountCompanies.validation.emailTooLong))
      .optional(),
    phone: yup
      .string()
      .max(50, t(keys.accountCompanies.validation.phoneTooLong))
      .optional(),
    website: yup
      .string()
      .max(500, t(keys.accountCompanies.validation.websiteTooLong))
      .optional(),
    address_street: yup
      .string()
      .max(255, t(keys.accountCompanies.validation.addressStreetTooLong))
      .optional(),
    address_city: yup
      .string()
      .max(100, t(keys.accountCompanies.validation.addressCityTooLong))
      .optional(),
    address_postal_code: yup
      .string()
      .max(20, t(keys.accountCompanies.validation.addressPostalCodeTooLong))
      .optional(),
    address_country: yup
      .string()
      .max(100, t(keys.accountCompanies.validation.addressCountryTooLong))
      .optional(),
    vat_number: yup
      .string()
      .max(50, t(keys.accountCompanies.validation.vatNumberTooLong))
      .optional(),
    registration_number: yup
      .string()
      .max(100, t(keys.accountCompanies.validation.registrationNumberTooLong))
      .optional(),
  }), [t, keys])

  const isPending = isCreating || isUpdating
  const error = createError || updateError
  const isError = isCreateError || isUpdateError

  const form = useForm<CompanyFormValues>({
    resolver: yupResolver(schema),
    defaultValues: {
      company_name: "",
      email: "",
      phone: "",
      website: "",
      address_street: "",
      address_city: "",
      address_postal_code: "",
      address_country: "",
      vat_number: "",
      registration_number: "",
    }
  })

  // Reset form when company changes
  useEffect(() => {
    if (company) {
      form.reset({
        company_name: company.company_name || "",
        email: company.email || "",
        phone: company.phone || "",
        website: company.website || "",
        address_street: company.address_street || "",
        address_city: company.address_city || "",
        address_postal_code: company.address_postal_code || "",
        address_country: company.address_country || "",
        vat_number: company.vat_number || "",
        registration_number: company.registration_number || "",
      })
    } else {
      form.reset({
        company_name: "",
        email: "",
        phone: "",
        website: "",
        address_street: "",
        address_city: "",
        address_postal_code: "",
        address_country: "",
        vat_number: "",
        registration_number: "",
      })
    }
  }, [company, form])

  const onSubmit = (data: CompanyFormValues) => {
    if (isEditing && company) {
      // Update existing company
      updateCompany({
        params: { path: { id: company.id } },
        body: data,
      }, {
        onSuccess: () => {
          onClose()
          onSuccess?.()
        },
      })
    } else {
      // Create new company
      createCompany({
        body: data,
      }, {
        onSuccess: () => {
          onClose()
          onSuccess?.()
        },
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="w-[calc(100vw-1rem)] max-w-2xl max-h-[calc(100vh-2rem)] md:max-h-[80vh] overflow-y-auto mx-2 md:mx-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? (t(keys.accountCompanies.editTitle)) : (t(keys.accountCompanies.createTitle))}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="company_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t(keys.accountCompanies.companyName)} <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.email)}</FormLabel>
                      <FormControl>
                        <Input type="email" {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.phone)}</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="address_street"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.addressStreet)}</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="address_city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.addressCity)}</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="address_postal_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.addressPostalCode)}</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="address_country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.addressCountry)}</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.website)}</FormLabel>
                      <FormControl>
                        <Input type="url" {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="vat_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.vatNumber)}</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="registration_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.accountCompanies.registrationNumber)}</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isPending} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {isError && error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {error?.error.message || (t(keys.accountCompanies.error))}
                </AlertDescription>
              </Alert>
            )}

            <div className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose} disabled={isPending} className="min-h-[44px]">
                {t(keys.accountCompanies.cancel)}
              </Button>
              <Button type="submit" disabled={isPending} className="min-h-[44px]">
                {isPending
                  ? (isEditing ? (t(keys.accountCompanies.updating)) : (t(keys.accountCompanies.creating)))
                  : (t(keys.accountCompanies.save))
                }
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}