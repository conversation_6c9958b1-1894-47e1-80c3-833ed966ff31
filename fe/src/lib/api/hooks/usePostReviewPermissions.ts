import { useQuery } from '@tanstack/react-query';
import { useMyPostReview, usePostReviews } from './usePostReviews';
import { useAuth } from '@/contexts/auth-context.tsx';

/**
 * Hook to determine post review permissions for the current user.
 * Handles complex logic around who can review posts based on:
 * 1. Hub participant role (admin, reviewer, reviewer_creator)
 * 2. Post reviewer assignment (must be assigned to specific post)
 * 3. User's existing review status
 */
export function usePostReviewPermissions(postId: number, hubId?: number) {
  const { user } = useAuth();
  const { data: myReview, isLoading: myReviewLoading } = useMyPostReview(postId);
  const { data: allReviews, isLoading: allReviewsLoading } = usePostReviews(postId);

  // Get user's hub participant info (this would come from hub context or API)
  const { data: userParticipant, isLoading: participantLoading } = useQuery({
    queryKey: ['hub-participant', hubId, user?.id],
    queryFn: async () => {
      if (!hubId || !user?.id) return null;
      
      // This would be an API call to get user's participant info
      // For now, we'll simulate this - in real implementation, you'd call:
      // return fetchClient.GET('/api/hubs/{hubId}/participants/me', { params: { path: { hubId } } });
      
      return null; // Placeholder - implement actual API call
    },
    enabled: !!hubId && !!user?.id,
  });

  const isLoading = myReviewLoading || allReviewsLoading || participantLoading;

  // Determine if user has a reviewer role
  const hasReviewerRole = (role: HubParticipantRole | string | undefined): boolean => {
    return role === 'admin' || role === 'reviewer' || role === 'reviewer_creator';
  };

  // Check if user is assigned as reviewer for this specific post
  const isAssignedReviewer = (): boolean => {
    if (!allReviews?.reviews || !user?.id) return false;
    
    // Check if current user is in the list of assigned reviewers
    return allReviews.reviews.some(review => 
      review.reviewer_id === user.id || 
      review.reviewer_email === user.email
    );
  };

  // Determine overall review permission
  const canReview = (): boolean => {
    if (!user) return false;
    
    // Must have reviewer role AND be assigned to this specific post
    const hasRole = hasReviewerRole(userParticipant?.role);
    const isAssigned = isAssignedReviewer();
    
    return hasRole && isAssigned;
  };

  // Get review status for current user
  const getMyReviewStatus = () => {
    if (!myReview) return 'not_assigned';
    if (!myReview.reviewed_at) return 'assigned';
    return myReview.decision;
  };

  // Check if user can see reviews (broader permission than reviewing)
  const canViewReviews = (): boolean => {
    if (!user || !userParticipant) return false;
    
    // All hub participants can view reviews, but with different visibility
    return true;
  };

  // Get user's reviewer assignment info
  const getReviewerAssignment = () => {
    if (!allReviews?.reviews || !user) return null;
    
    return allReviews.reviews.find(review => 
      review.reviewer_id === user.id || 
      review.reviewer_email === user.email
    );
  };

  return {
    // Loading states
    isLoading,
    
    // Permission flags
    canReview: canReview(),
    canViewReviews: canViewReviews(),
    hasReviewerRole: hasReviewerRole(userParticipant?.role),
    isAssignedReviewer: isAssignedReviewer(),
    
    // Review status
    myReviewStatus: getMyReviewStatus(),
    hasExistingReview: !!myReview,
    myReview,
    
    // Assignment info
    reviewerAssignment: getReviewerAssignment(),
    
    // User context
    userRole: userParticipant?.role,
    userId: user?.id,
    userEmail: user?.email,
    
    // Review statistics
    totalReviewers: allReviews?.review_summary.total_reviewers || 0,
    completedReviews: (allReviews?.review_summary.approved_reviews || 0) + 
                     (allReviews?.review_summary.rework_reviews || 0),
    pendingReviews: allReviews?.review_summary.pending_reviews || 0,
  };
}

/**
 * Hook to get reviewer role information for display purposes.
 * Helps determine what UI elements to show based on user's role.
 */
export function useReviewerRoleInfo(userRole: HubParticipantRole | string | undefined) {
  const getRoleDisplayInfo = () => {
    switch (userRole) {
      case 'admin':
        return {
          label: 'Admin',
          description: 'Can review any post and manage hub',
          canReview: true,
          canAssignReviewers: true,
          canManageHub: true,
        };
      case 'reviewer':
        return {
          label: 'Reviewer',
          description: 'Can review assigned posts',
          canReview: true,
          canAssignReviewers: false,
          canManageHub: false,
        };
      case 'reviewer_creator':
        return {
          label: 'Reviewer & Creator',
          description: 'Can create posts and review others',
          canReview: true,
          canAssignReviewers: false,
          canManageHub: false,
        };
      case 'content_creator':
        return {
          label: 'Content Creator',
          description: 'Can create posts but not review',
          canReview: false,
          canAssignReviewers: false,
          canManageHub: false,
        };
      default:
        return {
          label: 'Unknown',
          description: 'Role not recognized',
          canReview: false,
          canAssignReviewers: false,
          canManageHub: false,
        };
    }
  };

  return getRoleDisplayInfo();
}

/**
 * Hook to determine what review-related actions a user can perform.
 * Combines role permissions with specific post context.
 */
export function useReviewActions(postId: number, hubId?: number) {
  const permissions = usePostReviewPermissions(postId, hubId);
  const roleInfo = useReviewerRoleInfo(permissions.userRole);

  return {
    // Review actions
    canSubmitReview: permissions.canReview && permissions.isAssignedReviewer,
    canEditReview: permissions.canReview && permissions.hasExistingReview,
    canViewAllReviews: permissions.canViewReviews,
    
    // Assignment actions (for admins)
    canAssignReviewers: roleInfo.canAssignReviewers,
    canRemoveReviewers: roleInfo.canAssignReviewers,
    
    // Display flags
    showReviewPanel: permissions.canReview || permissions.canViewReviews,
    showReviewForm: permissions.canReview && permissions.isAssignedReviewer,
    showReviewStatus: true, // Everyone can see review status
    
    // Status information
    reviewButtonText: permissions.hasExistingReview ? 'Edit Review' : 'Submit Review',
    reviewStatusText: getReviewStatusText(permissions.myReviewStatus),
    
    // Loading state
    isLoading: permissions.isLoading,
  };
}

/**
 * Helper function to get human-readable review status text.
 */
function getReviewStatusText(status: string): string {
  switch (status) {
    case 'approved':
      return 'You approved this post';
    case 'rework':
      return 'You requested rework';
    case 'pending':
      return 'Review pending';
    case 'assigned':
      return 'Assigned for review';
    case 'not_assigned':
      return 'Not assigned as reviewer';
    default:
      return 'Unknown status';
  }
}
