import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { fetchClient } from '@/lib/api/client';
import type { 
  PostReviewCreateRequest, 
  PostReviewResponse, 
  PostReviewListResponse 
} from '@/lib/types/api';

/**
 * React Query hooks for post review functionality.
 * Provides type-safe API calls for creating, updating, and fetching post reviews.
 */

// ============================================================================
// QUERY KEYS
// ============================================================================

export const postReviewKeys = {
  all: ['post-reviews'] as const,
  lists: () => [...postReviewKeys.all, 'list'] as const,
  list: (postId: number) => [...postReviewKeys.lists(), postId] as const,
  details: () => [...postReviewKeys.all, 'detail'] as const,
  detail: (postId: number) => [...postReviewKeys.details(), postId] as const,
  myReview: (postId: number) => [...postReviewKeys.all, 'my-review', postId] as const,
};

// ============================================================================
// QUERY HOOKS
// ============================================================================

/**
 * Hook to fetch all reviews for a specific post.
 * Includes review summary statistics.
 */
export function usePostReviews(
  postId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return useQuery({
    queryKey: postReviewKeys.list(postId),
    queryFn: async (): Promise<PostReviewListResponse> => {
      const response = await fetchClient.GET('/api/posts/{postId}/reviews', {
        params: { path: { postId } },
      });

      if (response.error) {
        throw new Error(response.error.message || 'Failed to fetch post reviews');
      }

      return response.data as PostReviewListResponse;
    },
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 1000 * 60 * 5, // 5 minutes
  });
}

/**
 * Hook to fetch the current user's review for a specific post.
 * Returns null if no review exists.
 */
export function useMyPostReview(
  postId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return useQuery({
    queryKey: postReviewKeys.myReview(postId),
    queryFn: async (): Promise<PostReviewResponse | null> => {
      const response = await fetchClient.GET('/api/posts/{postId}/reviews/my', {
        params: { path: { postId } },
      });

      if (response.error) {
        // If 404, user has no review - return null
        if (response.error.status === 404) {
          return null;
        }
        throw new Error(response.error.message || 'Failed to fetch my review');
      }

      return response.data as PostReviewResponse;
    },
    enabled: options?.enabled ?? true,
    staleTime: options?.staleTime ?? 1000 * 60 * 5, // 5 minutes
  });
}

// ============================================================================
// MUTATION HOOKS
// ============================================================================

/**
 * Hook to create or update a post review (upsert behavior).
 * Automatically invalidates related queries on success.
 */
export function useCreateOrUpdatePostReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      postId,
      review,
    }: {
      postId: number;
      review: PostReviewCreateRequest;
    }): Promise<PostReviewResponse> => {
      const response = await fetchClient.POST('/api/posts/{postId}/reviews', {
        params: { path: { postId } },
        body: review,
      });

      if (response.error) {
        throw new Error(response.error.message || 'Failed to submit review');
      }

      return response.data as PostReviewResponse;
    },
    onSuccess: (data, variables) => {
      const { postId } = variables;

      // Invalidate and refetch post reviews list
      queryClient.invalidateQueries({
        queryKey: postReviewKeys.list(postId),
      });

      // Invalidate and refetch user's review
      queryClient.invalidateQueries({
        queryKey: postReviewKeys.myReview(postId),
      });

      // Invalidate post details to update review status
      queryClient.invalidateQueries({
        queryKey: ['posts', 'detail', postId],
      });

      // Invalidate posts list to update review status indicators
      queryClient.invalidateQueries({
        queryKey: ['posts', 'list'],
      });
    },
    onError: (error) => {
      console.error('Failed to submit review:', error);
    },
  });
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Hook to check if the current user can review a specific post.
 * Based on post permissions and user's review status.
 */
export function useCanReviewPost(postId: number) {
  const { data: myReview } = useMyPostReview(postId);
  
  // This would typically come from post details or user permissions
  // For now, we'll assume if we can fetch reviews, we can review
  return {
    canReview: true, // This should be determined by post permissions
    hasExistingReview: !!myReview,
    existingReview: myReview,
  };
}

/**
 * Hook to get review statistics for a post.
 * Useful for displaying review progress indicators.
 */
export function usePostReviewStats(postId: number) {
  const { data: reviewsData, isLoading } = usePostReviews(postId);

  if (isLoading || !reviewsData) {
    return {
      isLoading,
      totalReviewers: 0,
      pendingReviews: 0,
      approvedReviews: 0,
      reworkReviews: 0,
      completionPercentage: 0,
      overallStatus: 'pending' as const,
    };
  }

  const { review_summary } = reviewsData;
  const completionPercentage = review_summary.total_reviewers > 0 
    ? Math.round(((review_summary.approved_reviews + review_summary.rework_reviews) / review_summary.total_reviewers) * 100)
    : 0;

  // Determine overall status based on reviews
  let overallStatus: 'pending' | 'approved' | 'rework' = 'pending';
  if (review_summary.rework_reviews > 0) {
    overallStatus = 'rework';
  } else if (review_summary.approved_reviews === review_summary.total_reviewers && review_summary.total_reviewers > 0) {
    overallStatus = 'approved';
  }

  return {
    isLoading: false,
    totalReviewers: review_summary.total_reviewers,
    pendingReviews: review_summary.pending_reviews,
    approvedReviews: review_summary.approved_reviews,
    reworkReviews: review_summary.rework_reviews,
    completionPercentage,
    overallStatus,
  };
}
