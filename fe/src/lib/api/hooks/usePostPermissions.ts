import { useMemo } from 'react';
import type { PostResponse, PostListItemResponse } from '@/lib/types/api';

/**
 * Simplified hook that extracts permission data from post responses.
 * This replaces the complex usePostReviewPermissions hook by using
 * permission data already calculated server-side.
 */
export function usePostPermissions(post: PostResponse | PostListItemResponse | null) {
  return useMemo(() => {
    if (!post) {
      return {
        // Loading states
        isLoading: false,
        
        // Permission flags
        canReview: false,
        canViewReviews: false,
        canEdit: false,
        canComment: false,
        canAssignReviewers: false,
        hasReviewerRole: false,
        isAssignedReviewer: false,
        
        // Review status
        myReviewStatus: 'not_assigned' as const,
        hasExistingReview: false,
        
        // User context
        userRole: null,
        
        // Review statistics (only available for detailed post response)
        totalReviewers: 0,
        completedReviews: 0,
        pendingReviews: 0,
      };
    }

    // Handle PostResponse (detailed post)
    if ('permissions' in post) {
      const permissions = post.permissions;
      const assignedReviewers = post.assigned_reviewers || [];
      
      return {
        // Loading states
        isLoading: false,
        
        // Permission flags
        canReview: permissions.can_review,
        canViewReviews: true, // All participants can view reviews
        canEdit: permissions.can_edit,
        canComment: permissions.can_comment,
        canAssignReviewers: permissions.can_assign_reviewers,
        hasReviewerRole: permissions.has_reviewer_role,
        isAssignedReviewer: permissions.is_assigned_reviewer,
        
        // Review status
        myReviewStatus: permissions.my_review_status || 'not_assigned',
        hasExistingReview: permissions.my_review_status !== null && permissions.my_review_status !== 'pending',
        
        // User context
        userRole: permissions.user_role,
        
        // Review statistics
        totalReviewers: assignedReviewers.length,
        completedReviews: assignedReviewers.filter(r => r.status === 'approved' || r.status === 'rework').length,
        pendingReviews: assignedReviewers.filter(r => r.status === 'pending').length,
      };
    }

    // Handle PostListItemResponse (list item)
    return {
      // Loading states
      isLoading: false,
      
      // Permission flags
      canReview: post.can_review,
      canViewReviews: true, // All participants can view reviews
      canEdit: post.can_edit,
      canComment: post.can_comment,
      canAssignReviewers: false, // Not available in list response
      hasReviewerRole: post.has_reviewer_role,
      isAssignedReviewer: post.is_assigned_reviewer,
      
      // Review status
      myReviewStatus: post.my_review_status || 'not_assigned',
      hasExistingReview: post.my_review_status !== null && post.my_review_status !== 'pending',
      
      // User context
      userRole: post.user_role,
      
      // Review statistics (available in list response)
      totalReviewers: post.assigned_reviewer_count,
      completedReviews: post.approved_by_count, // This is a simplified count
      pendingReviews: Math.max(0, post.assigned_reviewer_count - post.approved_by_count),
    };
  }, [post]);
}

/**
 * Hook to get reviewer role information for display purposes.
 * Helps determine what UI elements to show based on user's role.
 */
export function useReviewerRoleInfo(userRole: string | null | undefined) {
  return useMemo(() => {
    switch (userRole) {
      case 'admin':
        return {
          label: 'Admin',
          description: 'Can review any post and manage hub',
          canReview: true,
          canAssignReviewers: true,
          canManageHub: true,
        };
      case 'reviewer':
        return {
          label: 'Reviewer',
          description: 'Can review assigned posts',
          canReview: true,
          canAssignReviewers: false,
          canManageHub: false,
        };
      case 'reviewer_creator':
        return {
          label: 'Reviewer & Creator',
          description: 'Can create posts and review others',
          canReview: true,
          canAssignReviewers: false,
          canManageHub: false,
        };
      case 'content_creator':
        return {
          label: 'Content Creator',
          description: 'Can create posts but not review',
          canReview: false,
          canAssignReviewers: false,
          canManageHub: false,
        };
      default:
        return {
          label: 'Unknown',
          description: 'Role not recognized',
          canReview: false,
          canAssignReviewers: false,
          canManageHub: false,
        };
    }
  }, [userRole]);
}

/**
 * Hook to determine what review-related actions a user can perform.
 * Combines role permissions with specific post context.
 */
export function useReviewActions(post: PostResponse | PostListItemResponse | null) {
  const permissions = usePostPermissions(post);
  const roleInfo = useReviewerRoleInfo(permissions.userRole);

  return useMemo(() => ({
    // Review actions
    canSubmitReview: permissions.canReview && permissions.isAssignedReviewer,
    canEditReview: permissions.canReview && permissions.hasExistingReview,
    canViewAllReviews: permissions.canViewReviews,
    
    // Assignment actions (for admins)
    canAssignReviewers: roleInfo.canAssignReviewers,
    canRemoveReviewers: roleInfo.canAssignReviewers,
    
    // Display flags
    showReviewPanel: permissions.canReview || permissions.canViewReviews,
    showReviewForm: permissions.canReview && permissions.isAssignedReviewer,
    showReviewStatus: true, // Everyone can see review status
    
    // Status information
    reviewButtonText: permissions.hasExistingReview ? 'Edit Review' : 'Submit Review',
    reviewStatusText: getReviewStatusText(permissions.myReviewStatus),
    
    // Loading state
    isLoading: permissions.isLoading,
  }), [permissions, roleInfo]);
}

/**
 * Helper function to get human-readable review status text.
 */
function getReviewStatusText(status: string): string {
  switch (status) {
    case 'approved':
      return 'You approved this post';
    case 'rework':
      return 'You requested rework';
    case 'pending':
      return 'Review pending';
    case 'assigned':
      return 'Assigned for review';
    case 'not_assigned':
      return 'Not assigned as reviewer';
    default:
      return 'Unknown status';
  }
}
