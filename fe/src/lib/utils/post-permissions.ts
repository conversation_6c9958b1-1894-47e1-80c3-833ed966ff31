import type { components } from '@/lib/api/v1';

type PostListItemResponse = components['schemas']['PostListItemResponse'];
type PostResponse = components['schemas']['PostResponse'];

/**
 * Utility functions to extract permission data from post responses.
 * This replaces the complex usePostReviewPermissions hook by using
 * permission data already calculated server-side.
 */

export interface PostPermissions {
  // Permission flags
  canReview: boolean;
  canViewReviews: boolean;
  canEdit: boolean;
  canComment: boolean;
  canAssignReviewers: boolean;
  hasReviewerRole: boolean;
  isAssignedReviewer: boolean;
  
  // Review status
  myReviewStatus: string | null;
  hasExistingReview: boolean;
  
  // User context
  userRole: string | null;
  
  // Review statistics
  totalReviewers: number;
  completedReviews: number;
  pendingReviews: number;
}

/**
 * Extracts permission data from a post response (either list item or detailed post).
 */
export function getPostPermissions(post: PostListItemResponse | PostResponse | null): PostPermissions {
  if (!post) {
    return {
      canReview: false,
      canViewReviews: false,
      canEdit: false,
      canComment: false,
      canAssignReviewers: false,
      hasReviewerRole: false,
      isAssignedReviewer: false,
      myReviewStatus: null,
      hasExistingReview: false,
      userRole: null,
      totalReviewers: 0,
      completedReviews: 0,
      pendingReviews: 0,
    };
  }

  // Handle PostResponse (detailed post)
  if ('permissions' in post && post.permissions) {
    const permissions = post.permissions;
    const assignedReviewers = post.assigned_reviewers || [];
    
    return {
      canReview: permissions.can_review ?? false,
      canViewReviews: true, // All participants can view reviews
      canEdit: permissions.can_edit ?? false,
      canComment: permissions.can_comment ?? false,
      canAssignReviewers: permissions.can_assign_reviewers ?? false,
      hasReviewerRole: permissions.has_reviewer_role ?? false,
      isAssignedReviewer: permissions.is_assigned_reviewer ?? false,
      myReviewStatus: permissions.my_review_status ?? null,
      hasExistingReview: permissions.my_review_status !== null && permissions.my_review_status !== 'pending',
      userRole: permissions.user_role ?? null,
      totalReviewers: assignedReviewers.length,
      completedReviews: assignedReviewers.filter(r => r.status === 'approved' || r.status === 'rework').length,
      pendingReviews: assignedReviewers.filter(r => r.status === 'pending').length,
    };
  }

  // Handle PostListItemResponse (list item)
  return {
    canReview: post.can_review ?? false,
    canViewReviews: true, // All participants can view reviews
    canEdit: post.can_edit ?? false,
    canComment: post.can_comment ?? false,
    canAssignReviewers: false, // Not available in list response
    hasReviewerRole: post.has_reviewer_role ?? false,
    isAssignedReviewer: post.is_assigned_reviewer ?? false,
    myReviewStatus: post.my_review_status ?? null,
    hasExistingReview: post.my_review_status !== null && post.my_review_status !== 'pending',
    userRole: post.user_role ?? null,
    totalReviewers: post.assigned_reviewer_count ?? 0,
    completedReviews: post.approved_by_count ?? 0,
    pendingReviews: Math.max(0, (post.assigned_reviewer_count ?? 0) - (post.approved_by_count ?? 0)),
  };
}

/**
 * Gets reviewer role information for display purposes.
 */
export function getReviewerRoleInfo(userRole: string | null | undefined) {
  switch (userRole) {
    case 'admin':
      return {
        label: 'Admin',
        description: 'Can review any post and manage hub',
        canReview: true,
        canAssignReviewers: true,
        canManageHub: true,
      };
    case 'reviewer':
      return {
        label: 'Reviewer',
        description: 'Can review assigned posts',
        canReview: true,
        canAssignReviewers: false,
        canManageHub: false,
      };
    case 'reviewer_creator':
      return {
        label: 'Reviewer & Creator',
        description: 'Can create posts and review others',
        canReview: true,
        canAssignReviewers: false,
        canManageHub: false,
      };
    case 'content_creator':
      return {
        label: 'Content Creator',
        description: 'Can create posts but not review',
        canReview: false,
        canAssignReviewers: false,
        canManageHub: false,
      };
    default:
      return {
        label: 'Unknown',
        description: 'Role not recognized',
        canReview: false,
        canAssignReviewers: false,
        canManageHub: false,
      };
  }
}

/**
 * Determines what review-related actions a user can perform.
 */
export function getReviewActions(post: PostListItemResponse | PostResponse | null) {
  const permissions = getPostPermissions(post);
  const roleInfo = getReviewerRoleInfo(permissions.userRole);

  return {
    // Review actions
    canSubmitReview: permissions.canReview && permissions.isAssignedReviewer,
    canEditReview: permissions.canReview && permissions.hasExistingReview,
    canViewAllReviews: permissions.canViewReviews,
    
    // Assignment actions (for admins)
    canAssignReviewers: roleInfo.canAssignReviewers,
    canRemoveReviewers: roleInfo.canAssignReviewers,
    
    // Display flags
    showReviewPanel: permissions.canReview || permissions.canViewReviews,
    showReviewForm: permissions.canReview && permissions.isAssignedReviewer,
    showReviewStatus: true, // Everyone can see review status
    
    // Status information
    reviewButtonText: permissions.hasExistingReview ? 'Edit Review' : 'Submit Review',
    reviewStatusText: getReviewStatusText(permissions.myReviewStatus),
  };
}

/**
 * Helper function to get human-readable review status text.
 */
export function getReviewStatusText(status: string | null): string {
  switch (status) {
    case 'approved':
      return 'You approved this post';
    case 'rework':
      return 'You requested rework';
    case 'pending':
      return 'Review pending';
    case 'assigned':
      return 'Assigned for review';
    case null:
    case 'not_assigned':
      return 'Not assigned as reviewer';
    default:
      return 'Unknown status';
  }
}
