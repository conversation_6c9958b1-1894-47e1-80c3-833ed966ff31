import { useState, useMemo, useEffect, useCallback } from "react"
import { useSearchParams } from "react-router"
import { Tag, Plus, AlertCircle, Search } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CardSkeletonGrid } from "@/components/ui/card-skeleton"
import { ResponsiveCardGrid } from "@/components/ui/responsive-card-grid"
import { BrandCard } from '@/components/brand/brand-card'
import { EditBrandDialog } from "@/components/brand/edit-brand-dialog"
import { DeleteBrandDialog } from "@/components/brand/delete-brand-dialog"
import { useBrands, useBrand } from "@/hooks/brands"
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useDebounce } from "@/lib/utils"
import type { BrandDisplayData } from '@/components/brand/types'
import { convertBrandListToDisplayData } from '@/components/brand/types'

export default function BrandsPage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const [editingBrandId, setEditingBrandId] = useState<number | null>(null)
  const [deletingBrandId, setDeletingBrandId] = useState<number | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const pageSize = 12 // 3x4 grid on large screens

  const { t, keys } = useTranslations()

  // Parse filters from URL
  const getFiltersFromUrl = useCallback(() => {
    return {
      name: searchParams.get('name') || '',
      page: parseInt(searchParams.get('page') || '0'),
    }
  }, [searchParams])

  const [filters, setFilters] = useState(getFiltersFromUrl())

  // Debounce the name filter to avoid excessive API calls
  const debouncedNameFilter = useDebounce(filters.name, 400)

  // Update URL when filters change
  const updateFiltersInUrl = (newFilters: typeof filters) => {
    const params = new URLSearchParams()

    if (newFilters.name) params.set('name', newFilters.name)
    if (newFilters.page && newFilters.page > 0) params.set('page', newFilters.page.toString())

    setSearchParams(params)
    setFilters(newFilters)
  }

  // Sync filters with URL on mount
  useEffect(() => {
    const urlFilters = getFiltersFromUrl()
    setFilters(urlFilters)
  }, [searchParams, getFiltersFromUrl])

  const { data: brandsPage, isLoading, error, isError, refetch } = useBrands(
    debouncedNameFilter || undefined,
    filters.page,
    pageSize
  )

  // Fetch full brand data when editing
  const { data: editingBrand } = useBrand(editingBrandId!, !!editingBrandId)
  const { data: deletingBrand } = useBrand(deletingBrandId!, !!deletingBrandId)

  // Convert API response to display data
  const brandDisplayData: BrandDisplayData[] = useMemo(() => {
    if (!brandsPage?.content) return []

    return brandsPage.content.map(convertBrandListToDisplayData)
  }, [brandsPage])

  const handleCreateNew = () => {
    setIsCreating(true)
    setEditingBrandId(null)
  }

  const handleEdit = (brandData: BrandDisplayData) => {
    setIsCreating(false)
    setEditingBrandId(brandData.id)
  }

  const handleDelete = (brandData: BrandDisplayData) => {
    setDeletingBrandId(brandData.id)
  }

  const handleCloseDialog = () => {
    setEditingBrandId(null)
    setIsCreating(false)
  }

  const handleSuccess = () => {
    // Dialogs will close automatically via their onSuccess handlers
    // Data will be automatically refreshed via React Query cache invalidation
  }

  const handleSearchChange = (value: string) => {
    updateFiltersInUrl({ ...filters, name: value, page: 0 }) // Reset to first page when searching
  }

  const handlePageChange = (newPage: number) => {
    updateFiltersInUrl({ ...filters, page: newPage })
  }

  if (isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t(keys.brands.title)}</h1>
            <p className="text-muted-foreground">{t(keys.brands.description)}</p>
          </div>
          <Button onClick={handleCreateNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {t(keys.brands.createNew)}
          </Button>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t(keys.brands.searchPlaceholder)}
              value={filters.name}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <CardSkeletonGrid count={6} />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t(keys.brands.title)}</h1>
            <p className="text-muted-foreground">{t(keys.brands.description)}</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error?.error?.message || t(keys.brands.error)}
          </AlertDescription>
        </Alert>

        <div className="flex justify-center">
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const hasBrands = brandDisplayData && brandDisplayData.length > 0
  const totalPages = brandsPage?.total_pages || 0
  const totalElements = brandsPage?.total_elements || 0

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{t(keys.brands.title)}</h1>
          <p className="text-muted-foreground">{t(keys.brands.description)}</p>
        </div>
        <Button onClick={handleCreateNew} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          {t(keys.brands.createNew)}
        </Button>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t(keys.brands.searchPlaceholder)}
            value={filters.name}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        {totalElements > 0 && (
          <p className="text-sm text-muted-foreground">
            {t(keys.brands.showingResults, { count: brandDisplayData.length, total: totalElements })}
          </p>
        )}
      </div>

      {hasBrands ? (
        <>
          <ResponsiveCardGrid>
            {brandDisplayData.map((brand) => (
              <BrandCard
                key={brand.id}
                brand={brand}
                onEdit={() => handleEdit(brand)}
                onDelete={() => handleDelete(brand)}
              />
            ))}
          </ResponsiveCardGrid>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(filters.page - 1)}
                disabled={filters.page === 0}
              >
                {t(keys.brands.previous)}
              </Button>

              <span className="text-sm text-muted-foreground px-4">
                {t(keys.brands.page)} {filters.page + 1} {t(keys.brands.of)} {totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(filters.page + 1)}
                disabled={filters.page >= totalPages - 1}
              >
                {t(keys.brands.next)}
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center min-h-[500px] text-center max-w-2xl mx-auto px-4">
          <div className="mb-6 p-4 bg-muted rounded-full">
            <Tag className="h-12 w-12 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-semibold mb-3">
            {filters.name
              ? t(keys.brands.noBrandsForSearch, { searchTerm: filters.name })
              : t(keys.brands.noBrands)
            }
          </h2>
          <p className="text-muted-foreground text-lg mb-8 leading-relaxed">
            {filters.name
              ? t(keys.brands.noBrandsForSearchDescription)
              : t(keys.brands.noBrandsDescription)
            }
          </p>
          <Button onClick={handleCreateNew} className="flex items-center gap-2" size="lg">
            <Plus className="h-5 w-5" />
            {t(keys.brands.createNew)}
          </Button>
        </div>
      )}

      <EditBrandDialog
        brand={isCreating ? null : editingBrand || null}
        open={isCreating || !!editingBrandId}
        onClose={handleCloseDialog}
        onSuccess={handleSuccess}
      />

      <DeleteBrandDialog
        brand={deletingBrand || null}
        open={!!deletingBrandId}
        onClose={() => setDeletingBrandId(null)}
        onSuccess={handleSuccess}
      />
    </div>
  )
}
