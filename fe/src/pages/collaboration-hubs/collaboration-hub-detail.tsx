import { usePara<PERSON>, useNavigate } from "react-router"
import { ArrowLeft, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"

import { useCollaborationHub } from "@/hooks/collaboration-hubs"


import { ROUTES } from "@/router/routes"
import { <PERSON>b<PERSON>eader } from "@/components/collaboration-hub/hub-header"
import { HubTabs } from "@/components/collaboration-hub/hub-tabs"

export default function CollaborationHubDetailPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()


  const hubId = id ? parseInt(id, 10) : 0
  const { data: hub, isLoading, error } = useCollaborationHub(hubId, {
    enabled: !!hubId && hubId > 0
  })

  const handleBack = () => {
    navigate(ROUTES.COLLABORATION_HUBS)
  }

  if (!hubId || hubId <= 0) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hubs
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Invalid collaboration hub ID.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hubs
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load collaboration hub. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hubs
          </Button>
        </div>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="h-16 bg-muted rounded"></div>
          <div className="h-12 bg-muted rounded"></div>
          <div className="h-96 bg-muted rounded"></div>
        </div>
      </div>
    )
  }

  if (!hub) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hubs
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Collaboration hub not found.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Back Navigation */}
      <div className="flex items-center gap-4 p-4 md:p-8 pb-0">
        <Button variant="ghost" size="sm" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Hubs
        </Button>
      </div>

      {/* Hub Header */}
      <HubHeader hub={hub} />

      {/* Hub Tabs */}
      <div className="flex-1 px-4 md:px-8 pb-4 md:pb-8">
        <HubTabs hubId={hubId} />
      </div>
    </div>
  )
}
