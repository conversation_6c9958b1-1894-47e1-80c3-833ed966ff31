import { useState, useEffect } from "react"
import { useSearchParams } from "react-router"
import { Users, Plus, AlertCircle, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CardSkeletonGrid } from "@/components/ui/card-skeleton"
import { ResponsiveCardGrid } from "@/components/ui/responsive-card-grid"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CollaborationHubCard } from '@/components/collaboration-hub/collaboration-hub-card'
import { CreateCollaborationHubDialog } from "@/components/collaboration-hub/create-collaboration-hub-dialog"
import { useCollaborationHubs } from "@/hooks/collaboration-hubs"
import { useBrands } from "@/hooks/brands"
import { useDebounce } from "@/lib/utils"

export default function CollaborationHubsPage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  
  // Get filter values from URL params
  const nameFilter = searchParams.get('name') || ''
  const brandFilter = searchParams.get('brandId') || ''
  const page = parseInt(searchParams.get('page') || '0')
  
  // Local state for immediate UI updates
  const [localNameFilter, setLocalNameFilter] = useState(nameFilter)
  const [localBrandFilter, setLocalBrandFilter] = useState(brandFilter)
  
  // Debounce the name filter to avoid too many API calls
  const debouncedNameFilter = useDebounce(localNameFilter, 300)
  
  // Update URL params when debounced values change
  useEffect(() => {
    const newParams = new URLSearchParams(searchParams)
    
    if (debouncedNameFilter) {
      newParams.set('name', debouncedNameFilter)
    } else {
      newParams.delete('name')
    }
    
    if (localBrandFilter) {
      newParams.set('brandId', localBrandFilter)
    } else {
      newParams.delete('brandId')
    }
    
    // Reset to first page when filters change
    if (debouncedNameFilter !== nameFilter || localBrandFilter !== brandFilter) {
      newParams.delete('page')
    }
    
    setSearchParams(newParams)
  }, [debouncedNameFilter, localBrandFilter, searchParams, setSearchParams, nameFilter, brandFilter])

  // Fetch data
  const { 
    data: hubsResponse, 
    isLoading: hubsLoading, 
    error: hubsError 
  } = useCollaborationHubs(
    debouncedNameFilter || undefined,
    localBrandFilter ? parseInt(localBrandFilter) : undefined,
    page,
    20
  )

  const { data: brandsResponse } = useBrands()

  const hubs = hubsResponse?.content || []
  const totalPages = hubsResponse?.total_pages || 0
  const totalElements = hubsResponse?.total_elements || 0

  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams)
    if (newPage > 0) {
      newParams.set('page', newPage.toString())
    } else {
      newParams.delete('page')
    }
    setSearchParams(newParams)
  }

  const handleClearFilters = () => {
    setLocalNameFilter('')
    setLocalBrandFilter('')
    setSearchParams({})
  }

  const hasActiveFilters = localNameFilter || localBrandFilter

  const handleEditHub = (id: number) => {
    // TODO: Open edit dialog
    console.log('Edit hub:', id)
  }

  const handleDeleteHub = (id: number) => {
    // TODO: Open delete confirmation dialog
    console.log('Delete hub:', id)
  }

  if (hubsError) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-6 w-6" />
            <h1 className="text-3xl font-bold tracking-tight">Collaboration Hubs</h1>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load collaboration hubs. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-6 w-6" />
          <h1 className="text-3xl font-bold tracking-tight">Collaboration Hubs</h1>
        </div>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Hub
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search hubs by name..."
            value={localNameFilter}
            onChange={(e) => setLocalNameFilter(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={localBrandFilter || "all"} onValueChange={(value) => setLocalBrandFilter(value === "all" ? "" : value)}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="All brands" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All brands</SelectItem>
            {brandsResponse?.content?.map((brand) => (
              <SelectItem key={brand.id} value={brand.id.toString()}>
                {brand.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {hasActiveFilters && (
          <Button variant="outline" onClick={handleClearFilters}>
            Clear filters
          </Button>
        )}
      </div>

      {/* Content */}
      {hubsLoading ? (
        <CardSkeletonGrid />
      ) : hubs.length === 0 ? (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">
            {hasActiveFilters ? 'No hubs found' : 'No collaboration hubs yet'}
          </h3>
          <p className="mt-2 text-muted-foreground">
            {hasActiveFilters 
              ? 'Try adjusting your search filters.' 
              : 'Create your first collaboration hub to start working with your team and brand partners.'
            }
          </p>
          {!hasActiveFilters && (
            <Button className="mt-4" onClick={() => setCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Hub
            </Button>
          )}
        </div>
      ) : (
        <ResponsiveCardGrid>
          {hubs.map((hub) => (
            <CollaborationHubCard
              key={hub.id}
              hub={hub}
              onEdit={handleEditHub}
              onDelete={handleDeleteHub}
            />
          ))}
        </ResponsiveCardGrid>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {page * 20 + 1} to{' '}
            {Math.min((page + 1) * 20, totalElements)} of{' '}
            {totalElements} hubs
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 0}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {page + 1} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(page + 1)}
              disabled={page >= totalPages - 1}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Create Dialog */}
      <CreateCollaborationHubDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
      />
    </div>
  )
}
