# Frontend Usage Example

## Before (with Maps)
```typescript
// ❌ No type safety, no ID access
const invoice = useInvoice(invoiceId);
const issuerName = invoice.data?.issuer?.name; // any type
const issuerId = ???; // ID not available for combobox
```

## After (with POJOs and IDs)
```typescript
// ✅ Type-safe access with IDs
const invoice = useInvoice(invoiceId);
const issuerSnapshot = invoice.data?.issuer; // IssuerSnapshotResponse type
const issuerName = issuerSnapshot?.name; // string type
const issuerId = issuerSnapshot?.id; // Long type - available for combobox!

// ✅ Combobox can now show correct selection
<Combobox
  value={issuerId?.toString()}
  options={issuerOptions}
  // ... other props
/>
```

## API Response Structure
```json
{
  "id": 123,
  "invoice_number": "INV-2024-001",
  "issuer": {
    "id": 456,
    "name": "My Company Ltd",
    "address": "123 Business St",
    "city": "Business City",
    "postal_code": "12345",
    "country": "Country",
    "vat_number": "VAT123456",
    "registration_number": "REG789",
    "phone": "+**********",
    "email": "<EMAIL>",
    "website": "https://mycompany.com"
  },
  "recipient": {
    "id": 789,
    "name": "Client Brand Inc",
    "address": "456 Client Ave",
    "city": "Client City",
    "postal_code": "67890",
    "country": "Country",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "bank_details": {
    "id": 101,
    "account_name": "My Company Business Account",
    "bank_name": "Business Bank",
    "iban": "GB29 NWBK 6016 1331 9268 19",
    "bic": "NWBKGB2L"
  }
}
```
