# S3 Migration Guide: Account-Specific Buckets to Single Shared Bucket

## Overview

This migration changes the S3 storage architecture from using separate buckets per account to a single shared bucket with folder-based organization for better resource management and cost optimization.

## Changes Made

### Architecture Changes

**Before:**
- Bucket naming: `{bucketPrefix}-{accountId}-posts` (e.g., `collabhub-1-posts`, `collabhub-2-posts`)
- File structure: `{folder}/{filename}` within each account-specific bucket
- URL format: `https://collabhub-1-posts.s3.region.amazonaws.com/posts/file.jpg`

**After:**
- Single bucket: `{bucketName}` (e.g., `collabhub`)
- File structure: `{accountId}/{resourceType}/{filename}` (e.g., `1/posts/uuid.jpg`, `2/invoices/uuid.pdf`)
- URL format: `https://collabhub.s3.region.amazonaws.com/1/posts/file.jpg`

### Configuration Changes

**application.properties:**
```properties
# OLD
app.s3.bucket-prefix=collabhub

# NEW
app.s3.bucket-name=collabhub
```

### Code Changes

1. **S3Properties.java**
   - Changed `bucketPrefix` to `bucketName`
   - Added `getKeyPrefix(accountId, resourceType)` method
   - Added `ResourceType` constants class

2. **S3Service.java**
   - Updated `ensureBucketExists()` to be account-agnostic
   - Modified `uploadFile()` to use folder-based structure
   - Enhanced `deleteFile()` with account validation
   - Updated `generatePresignedUploadUrl()` for new structure
   - Enhanced `validateUploadedFile()` with account validation
   - Fixed `extractKeyFromUrl()` for single bucket structure

3. **PostService.java & PostController.java**
   - Updated method calls to use `S3Properties.ResourceType.POSTS`

## Resource Types

The following resource types are now supported:
- `posts` - Media files for collaboration hub posts
- `invoices` - PDF files and attachments for invoices
- `avatars` - User and brand avatar images
- `documents` - General document uploads

## Security Enhancements

1. **Account Isolation**: Files are organized by account ID in folder structure
2. **Access Validation**: All operations validate that files belong to the requesting account
3. **URL Validation**: File URLs are validated to ensure account ownership

## Migration Steps

### For Development Environment

1. **Update Configuration**
   ```bash
   # Update your local application.properties
   sed -i 's/app.s3.bucket-prefix/app.s3.bucket-name/g' be/src/main/resources/application.properties
   ```

2. **Clear Existing Data** (Development Only)
   ```bash
   # For MinIO development setup, you can clear existing buckets
   # This will remove all existing files - only do this in development!
   mc rm --recursive --force minio/collabhub-*
   ```

3. **Restart Application**
   - The application will automatically create the new shared bucket structure

### For Production Environment

⚠️ **IMPORTANT**: Production migration requires careful planning to avoid data loss.

1. **Backup Existing Data**
   ```bash
   # Create backup of all existing buckets
   aws s3 sync s3://collabhub-1-posts s3://backup-collabhub-1-posts
   aws s3 sync s3://collabhub-2-posts s3://backup-collabhub-2-posts
   # ... repeat for all account buckets
   ```

2. **Create Migration Script**
   ```bash
   #!/bin/bash
   # Example migration script (customize for your environment)
   
   # Create new shared bucket
   aws s3 mb s3://collabhub
   
   # Migrate files from account-specific buckets to shared bucket
   for account_id in $(aws s3api list-buckets --query 'Buckets[?starts_with(Name, `collabhub-`) && ends_with(Name, `-posts`)].Name' --output text | sed 's/collabhub-\([0-9]*\)-posts/\1/g'); do
     echo "Migrating account $account_id"
     aws s3 sync s3://collabhub-${account_id}-posts s3://collabhub/${account_id}/posts/
   done
   ```

3. **Update Configuration**
   ```bash
   # Update production configuration
   export S3_BUCKET_NAME=collabhub
   # Remove S3_BUCKET_PREFIX environment variable
   ```

4. **Deploy New Code**
   - Deploy the updated application code
   - Monitor logs for any issues

5. **Verify Migration**
   ```bash
   # Verify files are accessible in new structure
   aws s3 ls s3://collabhub/1/posts/
   aws s3 ls s3://collabhub/2/posts/
   ```

6. **Cleanup Old Buckets** (After verification)
   ```bash
   # Only after confirming everything works correctly
   aws s3 rb s3://collabhub-1-posts --force
   aws s3 rb s3://collabhub-2-posts --force
   # ... repeat for all old buckets
   ```

## Testing

1. **Upload Test**
   ```bash
   # Test file upload via API
   curl -X POST "http://localhost:8080/api/posts/media/upload" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -F "file=@test-image.jpg"
   ```

2. **Verify File Structure**
   ```bash
   # Check MinIO/S3 bucket structure
   mc ls minio/collabhub/1/posts/
   ```

3. **Test File Access**
   - Verify uploaded files are accessible via generated URLs
   - Test file deletion functionality
   - Verify account isolation (files from account 1 not accessible by account 2)

## Rollback Plan

If issues occur during production migration:

1. **Immediate Rollback**
   ```bash
   # Revert to old configuration
   export S3_BUCKET_PREFIX=collabhub
   unset S3_BUCKET_NAME
   
   # Deploy previous version of application
   ```

2. **Restore from Backup**
   ```bash
   # If data was lost, restore from backups
   aws s3 sync s3://backup-collabhub-1-posts s3://collabhub-1-posts
   ```

## Benefits

1. **Cost Optimization**: Reduced S3 management overhead with single bucket
2. **Simplified Management**: Easier to manage permissions and policies
3. **Better Organization**: Clear folder structure for different resource types
4. **Enhanced Security**: Account-based validation at application level
5. **Scalability**: No bucket limits per account

## Monitoring

After migration, monitor:
- File upload/download success rates
- S3 operation latencies
- Error logs for access violations
- Storage costs and usage patterns

## Support

If you encounter issues during migration:
1. Check application logs for detailed error messages
2. Verify S3 bucket permissions and policies
3. Ensure all environment variables are correctly set
4. Test with a small subset of data first
