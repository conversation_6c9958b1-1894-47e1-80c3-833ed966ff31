package com.collabhub.be.modules.collaborationhub.controller;

import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.service.CollaborationBriefService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing collaboration briefs within collaboration hubs.
 * Provides endpoints for creating, reading, updating, and deleting briefs.
 */
@RestController
@RequestMapping("/api/hubs/{hubId}/briefs")
@Tag(name = "Collaboration Briefs", description = "Collaboration hub brief management")
@PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRIEF_READ.permission)")
public class CollaborationBriefController {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationBriefController.class);

    private final CollaborationBriefService briefService;
    private final JwtClaimsService jwtClaimsService;

    public CollaborationBriefController(CollaborationBriefService briefService, 
                                      JwtClaimsService jwtClaimsService) {
        this.briefService = briefService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Creates a new collaboration brief.
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRIEF_WRITE.permission)")
    @Operation(summary = "Create a new collaboration brief",
               description = "Creates a new brief within a collaboration hub")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Brief created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Collaboration hub not found"),
        @ApiResponse(responseCode = "409", description = "Brief title already exists")
    })
    public ResponseEntity<CollaborationBriefResponse> createBrief(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Valid @RequestBody CollaborationBriefCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Creating brief '{}' in hub {} for account {} by user {}", 
                    request.getTitle(), hubId, userContext.getAccountId(), userContext.getUserId());

        CollaborationBriefResponse response = briefService.createBrief(
                hubId, request, userContext.getAccountId(), userContext.getUserId());

        logger.info("Created brief {} in hub {} for account {}", 
                   response.getId(), hubId, userContext.getAccountId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Gets a paginated list of collaboration briefs.
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRIEF_READ.permission)")
    @Operation(summary = "Get collaboration briefs",
               description = "Retrieves a paginated list of briefs within a collaboration hub with optional filtering")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Briefs retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Collaboration hub not found")
    })
    public ResponseEntity<PageResponse<CollaborationBriefListItemDto>> getBriefs(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Filter briefs by title (case-insensitive partial match)")
            @RequestParam(value = "title", required = false) String titleFilter,
            @Parameter(description = "Page number (0-based)")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)")
            @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving briefs for hub {} with filter '{}', page {}, size {} for account {} by user {}", 
                    hubId, titleFilter, page, size, userContext.getAccountId(), userContext.getUserId());

        PageRequest pageRequest = new PageRequest(page, size);
        PageResponse<CollaborationBriefListItemDto> response = briefService.getBriefs(
                hubId, pageRequest, titleFilter, userContext.getAccountId(), userContext.getUserId());

        logger.debug("Retrieved {} briefs for hub {} in account {}", 
                    response.getContent().size(), hubId, userContext.getAccountId());
        return ResponseEntity.ok(response);
    }

    /**
     * Gets a specific collaboration brief by ID.
     */
    @GetMapping("/{briefId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRIEF_READ.permission)")
    @Operation(summary = "Get collaboration brief by ID",
               description = "Retrieves a specific brief within a collaboration hub")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Brief retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Brief or collaboration hub not found")
    })
    public ResponseEntity<CollaborationBriefResponse> getBriefById(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Brief ID", required = true)
            @PathVariable Long briefId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving brief {} in hub {} for account {} by user {}", 
                    briefId, hubId, userContext.getAccountId(), userContext.getUserId());

        CollaborationBriefResponse response = briefService.getBriefById(
                hubId, briefId, userContext.getAccountId(), userContext.getUserId());

        logger.debug("Retrieved brief {} in hub {} for account {}", 
                    briefId, hubId, userContext.getAccountId());
        return ResponseEntity.ok(response);
    }

    /**
     * Updates an existing collaboration brief.
     */
    @PutMapping("/{briefId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRIEF_UPDATE.permission)")
    @Operation(summary = "Update collaboration brief",
               description = "Updates an existing brief within a collaboration hub")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Brief updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Brief or collaboration hub not found"),
        @ApiResponse(responseCode = "409", description = "Brief title already exists")
    })
    public ResponseEntity<CollaborationBriefResponse> updateBrief(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Brief ID", required = true)
            @PathVariable Long briefId,
            @Valid @RequestBody CollaborationBriefUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Updating brief {} in hub {} for account {} by user {}", 
                    briefId, hubId, userContext.getAccountId(), userContext.getUserId());

        CollaborationBriefResponse response = briefService.updateBrief(
                hubId, briefId, request, userContext.getAccountId(), userContext.getUserId());

        logger.info("Updated brief {} in hub {} for account {}", 
                   briefId, hubId, userContext.getAccountId());
        return ResponseEntity.ok(response);
    }

    /**
     * Deletes a collaboration brief.
     */
    @DeleteMapping("/{briefId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).BRIEF_DELETE.permission)")
    @Operation(summary = "Delete collaboration brief",
               description = "Deletes a brief within a collaboration hub")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Brief deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Brief or collaboration hub not found")
    })
    public ResponseEntity<Void> deleteBrief(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Brief ID", required = true)
            @PathVariable Long briefId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Deleting brief {} in hub {} for account {} by user {}", 
                    briefId, hubId, userContext.getAccountId(), userContext.getUserId());

        briefService.deleteBrief(hubId, briefId, userContext.getAccountId(), userContext.getUserId());

        logger.info("Deleted brief {} in hub {} for account {}", 
                   briefId, hubId, userContext.getAccountId());
        return ResponseEntity.noContent().build();
    }
}
