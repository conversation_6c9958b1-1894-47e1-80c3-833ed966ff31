package com.collabhub.be.modules.posts.repository;

import org.jooq.DSLContext;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.daos.PostReviewerDao;
import org.jooq.generated.tables.pojos.PostReviewer;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.tables.PostReviewer.POST_REVIEWER;
import static org.jooq.generated.tables.HubParticipant.HUB_PARTICIPANT;

/**
 * Repository for PostReviewer entity using jOOQ for database operations.
 * Provides type-safe database operations for post reviewer management.
 */
@Repository
public class PostReviewerRepositoryImpl extends PostReviewerDao {

    private final DSLContext dsl;

    public PostReviewerRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all reviewers assigned to a specific post.
     *
     * @param postId the post ID
     * @return list of post reviewers
     */
    public List<PostReviewer> findByPostId(Long postId) {
        return dsl.selectFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .orderBy(POST_REVIEWER.ASSIGNED_AT.asc())
                .fetchInto(PostReviewer.class);
    }

    /**
     * Finds a specific post reviewer assignment.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return optional post reviewer
     */
    public Optional<PostReviewer> findByPostIdAndParticipantId(Long postId, Long participantId) {
        return dsl.selectFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .fetchOptionalInto(PostReviewer.class);
    }

    /**
     * Creates a new post reviewer assignment.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return the created post reviewer
     */
    public PostReviewer createPostReviewer(Long postId, Long participantId) {
        PostReviewer postReviewer = new PostReviewer();
        postReviewer.setPostId(postId);
        postReviewer.setParticipantId(participantId);
        postReviewer.setAssignedAt(LocalDateTime.now());
        postReviewer.setReviewStatus(ReviewStatus.pending);
        
        insert(postReviewer);
        return postReviewer;
    }

    /**
     * Updates the review status and notes for a post reviewer.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @param status the new review status
     * @param reviewNotes the review notes
     * @return true if updated successfully
     */
    public boolean updateReviewStatus(Long postId, Long participantId, ReviewStatus status, String reviewNotes) {
        int updated = dsl.update(POST_REVIEWER)
                .set(POST_REVIEWER.REVIEW_STATUS, status)
                .set(POST_REVIEWER.REVIEW_NOTES, reviewNotes)
                .set(POST_REVIEWER.REVIEWED_AT, LocalDateTime.now())
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .execute();

        return updated > 0;
    }

    /**
     * Bulk loads review counts for multiple posts to avoid N+1 queries.
     * Returns a map of postId -> ReviewCounts containing total, approved, and rework counts.
     */
    public Map<Long, ReviewCounts> bulkCountReviewersByPostIds(List<Long> postIds) {
        if (postIds.isEmpty()) {
            return Map.of();
        }

        var results = dsl.select(
                POST_REVIEWER.POST_ID,
                POST_REVIEWER.REVIEW_STATUS,
                DSL.count().as("count")
        )
        .from(POST_REVIEWER)
        .where(POST_REVIEWER.POST_ID.in(postIds))
        .groupBy(POST_REVIEWER.POST_ID, POST_REVIEWER.REVIEW_STATUS)
        .fetch();

        Map<Long, ReviewCounts> reviewCountsMap = new HashMap<>();

        // Initialize all post IDs with zero counts
        for (Long postId : postIds) {
            reviewCountsMap.put(postId, new ReviewCounts(0, 0, 0));
        }

        // Populate actual counts
        for (var record : results) {
            Long postId = record.get(POST_REVIEWER.POST_ID);
            ReviewStatus status = record.get(POST_REVIEWER.REVIEW_STATUS);
            Integer count = record.get("count", Integer.class);

            ReviewCounts counts = reviewCountsMap.get(postId);
            counts.addToTotal(count);

            if (status == ReviewStatus.approved) {
                counts.addToApproved(count);
            } else if (status == ReviewStatus.rework) {
                counts.addToRework(count);
            }
        }

        return reviewCountsMap;
    }

    /**
     * Data class to hold review counts for a post.
     */
    public static class ReviewCounts {
        private int total;
        private int approved;
        private int rework;

        public ReviewCounts(int total, int approved, int rework) {
            this.total = total;
            this.approved = approved;
            this.rework = rework;
        }

        public void addToTotal(int count) {
            this.total += count;
        }

        public void addToApproved(int count) {
            this.approved += count;
        }

        public void addToRework(int count) {
            this.rework += count;
        }

        public int getTotal() { return total; }
        public int getApproved() { return approved; }
        public int getRework() { return rework; }
    }

    /**
     * Checks if a participant is assigned as a reviewer for a post.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return true if participant is assigned as reviewer
     */
    public boolean isParticipantAssignedAsReviewer(Long postId, Long participantId) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(POST_REVIEWER)
                        .where(POST_REVIEWER.POST_ID.eq(postId))
                        .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
        );
    }

    /**
     * Gets the review status for a specific user on a post.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return the review status, or null if not assigned
     */
    public ReviewStatus getUserReviewStatus(Long postId, Long participantId) {
        return dsl.select(POST_REVIEWER.REVIEW_STATUS)
                .from(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .fetchOneInto(ReviewStatus.class);
    }

    /**
     * Gets the count of reviewers by status for a post.
     *
     * @param postId the post ID
     * @param status the review status
     * @return count of reviewers with the specified status
     */
    public int countReviewersByStatus(Long postId, ReviewStatus status) {
        return dsl.selectCount()
                .from(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.REVIEW_STATUS.eq(status))
                .fetchOne(0, int.class);
    }

    /**
     * Gets the total count of assigned reviewers for a post.
     *
     * @param postId the post ID
     * @return total count of assigned reviewers
     */
    public int countTotalReviewers(Long postId) {
        return dsl.selectCount()
                .from(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .fetchOne(0, int.class);
    }

    /**
     * Finds all posts assigned to a specific participant for review.
     *
     * @param participantId the participant ID
     * @param status optional status filter
     * @return list of post IDs assigned to the participant
     */
    public List<Long> findPostIdsAssignedToParticipant(Long participantId, ReviewStatus status) {
        var query = dsl.select(POST_REVIEWER.POST_ID)
                .from(POST_REVIEWER)
                .where(POST_REVIEWER.PARTICIPANT_ID.eq(participantId));

        if (status != null) {
            query = query.and(POST_REVIEWER.REVIEW_STATUS.eq(status));
        }

        return query.fetch(POST_REVIEWER.POST_ID);
    }

    /**
     * Removes a reviewer assignment from a post.
     *
     * @param postId the post ID
     * @param participantId the participant ID
     * @return true if removed successfully
     */
    public boolean removeReviewerAssignment(Long postId, Long participantId) {
        int deleted = dsl.deleteFrom(POST_REVIEWER)
                .where(POST_REVIEWER.POST_ID.eq(postId))
                .and(POST_REVIEWER.PARTICIPANT_ID.eq(participantId))
                .execute();

        return deleted > 0;
    }

    /**
     * Creates or updates a post reviewer (upsert behavior).
     * If a review already exists, updates it; otherwise creates a new one.
     *
     * @param postReviewer the post reviewer to upsert
     * @return the upserted post reviewer
     */
    public PostReviewer upsertPostReviewer(PostReviewer postReviewer) {
        // Check if review already exists
        var existing = findByPostIdAndParticipantId(
                postReviewer.getPostId(),
                postReviewer.getParticipantId()
        );

        if (existing.isPresent()) {
            // Update existing review
            PostReviewer existingReviewer = existing.get();
            existingReviewer.setReviewStatus(postReviewer.getReviewStatus());
            existingReviewer.setReviewNotes(postReviewer.getReviewNotes());
            existingReviewer.setReviewedAt(postReviewer.getReviewedAt());

            update(existingReviewer);
            return existingReviewer;
        } else {
            // Create new review with assigned_at timestamp
            postReviewer.setAssignedAt(LocalDateTime.now());
            insert(postReviewer);
            return postReviewer;
        }
    }

    /**
     * Finds all reviews for a post with participant details to avoid N+1 queries.
     * Returns reviews with complete participant information.
     *
     * @param postId the post ID
     * @return list of reviews with participant details
     */
    public List<PostReviewerWithParticipant> findReviewsWithParticipantsByPostId(Long postId) {
        return dsl.select(
                POST_REVIEWER.ID,
                POST_REVIEWER.POST_ID,
                POST_REVIEWER.PARTICIPANT_ID,
                POST_REVIEWER.ASSIGNED_AT,
                POST_REVIEWER.REVIEW_STATUS,
                POST_REVIEWER.REVIEW_NOTES,
                POST_REVIEWER.REVIEWED_AT,
                HUB_PARTICIPANT.ID.as("participant_id"),
                HUB_PARTICIPANT.EMAIL,
                HUB_PARTICIPANT.NAME,
                HUB_PARTICIPANT.IS_EXTERNAL
        )
        .from(POST_REVIEWER)
        .join(HUB_PARTICIPANT).on(HUB_PARTICIPANT.ID.eq(POST_REVIEWER.PARTICIPANT_ID))
        .where(POST_REVIEWER.POST_ID.eq(postId))
        .orderBy(POST_REVIEWER.ASSIGNED_AT.asc())
        .fetchInto(PostReviewerWithParticipant.class);
    }

    /**
     * Data class to hold post reviewer with participant details.
     * Used to avoid N+1 queries when fetching reviews with participant information.
     */
    public static class PostReviewerWithParticipant {
        private Long id;
        private Long postId;
        private Long participantId;
        private LocalDateTime assignedAt;
        private ReviewStatus reviewStatus;
        private String reviewNotes;
        private LocalDateTime reviewedAt;
        private String email;
        private String name;
        private Boolean isExternal;

        public PostReviewerWithParticipant() {}

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getPostId() { return postId; }
        public void setPostId(Long postId) { this.postId = postId; }

        public Long getParticipantId() { return participantId; }
        public void setParticipantId(Long participantId) { this.participantId = participantId; }

        public LocalDateTime getAssignedAt() { return assignedAt; }
        public void setAssignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; }

        public ReviewStatus getReviewStatus() { return reviewStatus; }
        public void setReviewStatus(ReviewStatus reviewStatus) { this.reviewStatus = reviewStatus; }

        public String getReviewNotes() { return reviewNotes; }
        public void setReviewNotes(String reviewNotes) { this.reviewNotes = reviewNotes; }

        public LocalDateTime getReviewedAt() { return reviewedAt; }
        public void setReviewedAt(LocalDateTime reviewedAt) { this.reviewedAt = reviewedAt; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public Boolean getIsExternal() { return isExternal; }
        public void setIsExternal(Boolean isExternal) { this.isExternal = isExternal; }
    }
}
