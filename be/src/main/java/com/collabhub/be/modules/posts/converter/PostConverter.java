package com.collabhub.be.modules.posts.converter;

import com.collabhub.be.modules.posts.dto.*;
import com.collabhub.be.modules.media.converter.MediaConverter;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Converter for Post entities and DTOs.
 * Handles conversion between jOOQ POJOs and API DTOs with new media architecture.
 */
@Component
public class PostConverter {

    private static final Logger logger = LoggerFactory.getLogger(PostConverter.class);
    private final MediaConverter mediaConverter;

    public PostConverter(MediaConverter mediaConverter) {
        this.mediaConverter = mediaConverter;
    }

    /**
     * Converts a create request DTO to a jOOQ POJO for database insertion.
     * Note: Media handling is now done through junction tables, not stored in post entity.
     */
    public Post toPost(PostCreateRequest request, Long hubId, Long creatorParticipantId) {
        if (request == null) {
            return null;
        }

        Post post = new Post();
        post.setHubId(hubId);
        post.setCreatorParticipantId(creatorParticipantId);
        post.setCaption(request.getCaption());
        post.setReviewerNotes(request.getReviewerNotes());
        post.setReviewStatus(ReviewStatus.pending);
        post.setCreatedAt(LocalDateTime.now());
        post.setUpdatedAt(LocalDateTime.now());

        return post;
    }

    /**
     * Updates an existing post with data from update request.
     * Note: Media handling is now done through junction tables, not stored in post entity.
     */
    public void updatePost(Post post, PostUpdateRequest request) {
        if (request == null || post == null) {
            return;
        }

        if (request.getCaption() != null) {
            post.setCaption(request.getCaption());
        }

        if (request.getReviewerNotes() != null) {
            post.setReviewerNotes(request.getReviewerNotes());
        }

        post.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * Converts a jOOQ POJO to a detailed response DTO with media from junction table.
     */
    public PostResponse toResponse(Post post, List<Media> mediaList, PostResponse.PostCreator creator,
                                  List<PostResponse.PostReviewer> reviewers,
                                  PostResponse.PostPermissions permissions) {
        if (post == null) {
            return null;
        }

        List<MediaItem> mediaItems = mediaConverter.toMediaItems(mediaList);

        return new PostResponse(
                post.getId(),
                post.getCaption(),
                mediaItems,
                post.getReviewStatus(),
                post.getReviewerNotes(),
                creator,
                reviewers != null ? reviewers : List.of(),
                permissions,
                post.getCreatedAt(),
                post.getUpdatedAt()
        );
    }



    /**
     * Converts a jOOQ POJO to a detailed response DTO with media URLs for frontend consumption.
     */
    public PostResponse toResponseWithPresignedUrls(Post post, List<Media> mediaList, PostResponse.PostCreator creator,
                                                   List<PostResponse.PostReviewer> reviewers,
                                                   PostResponse.PostPermissions permissions) {
        if (post == null) {
            return null;
        }

        List<MediaItem> mediaItems = mediaConverter.toMediaItems(mediaList);

        return new PostResponse(
                post.getId(),
                post.getCaption(),
                mediaItems,
                post.getReviewStatus(),
                post.getReviewerNotes(),
                creator,
                reviewers != null ? reviewers : List.of(),
                permissions,
                post.getCreatedAt(),
                post.getUpdatedAt()
        );
    }


    /**
     * Converts a jOOQ POJO to a list item response DTO with media URLs for frontend consumption.
     */
    public PostListItemResponse toListItemWithPresignedUrls(Post post, List<Media> mediaList, PostListItemResponse.PostCreator creator,
                                                           ReviewStatus myReviewStatus, int commentCount,
                                                           int assignedReviewerCount, int approvedByCount,
                                                           boolean canEdit, boolean canReview, boolean canComment,
                                                           boolean isAssignedReviewer, boolean hasReviewerRole, String userRole) {
        if (post == null) {
            return null;
        }

        List<MediaItem> mediaItems = mediaConverter.toMediaItems(mediaList);
        int mediaCount = mediaItems.size();

        return new PostListItemResponse(
                post.getId(),
                post.getCaption(),
                mediaCount,
                mediaItems,
                post.getReviewStatus(),
                creator,
                myReviewStatus,
                commentCount,
                assignedReviewerCount,
                approvedByCount,
                post.getCreatedAt(),
                canEdit,
                canReview,
                canComment,
                isAssignedReviewer,
                hasReviewerRole,
                userRole
        );
    }



    /**
     * Creates a PostCreator DTO.
     */
    public PostResponse.PostCreator createPostCreator(Long id, String name, String email) {
        return new PostResponse.PostCreator(id, name, email);
    }

    /**
     * Creates a PostCreator DTO for list items.
     */
    public PostListItemResponse.PostCreator createListItemCreator(Long id, String name, String email) {
        return new PostListItemResponse.PostCreator(id, name, email);
    }

    /**
     * Creates a PostReviewer DTO.
     */
    public PostResponse.PostReviewer createPostReviewer(Long id, String name, String email, ReviewStatus status) {
        return new PostResponse.PostReviewer(id, name, email, status);
    }

    /**
     * Creates PostPermissions DTO.
     */
    public PostResponse.PostPermissions createPermissions(boolean canEdit, boolean canReview,
                                                         boolean canComment, boolean canAssignReviewers,
                                                         boolean isAssignedReviewer, boolean hasReviewerRole,
                                                         ReviewStatus myReviewStatus, String userRole) {
        return new PostResponse.PostPermissions(canEdit, canReview, canComment, canAssignReviewers,
                                               isAssignedReviewer, hasReviewerRole, myReviewStatus, userRole);
    }
}
