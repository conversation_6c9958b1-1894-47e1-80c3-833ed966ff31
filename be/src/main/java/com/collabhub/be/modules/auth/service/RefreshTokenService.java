package com.collabhub.be.modules.auth.service;

import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.repository.RefreshTokenRepository;
import org.jooq.generated.tables.pojos.RefreshToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing refresh tokens with rotation support.
 * Handles token generation, validation, and rotation according to OWASP best practices.
 */
@Service
@Transactional
public class RefreshTokenService {

    private static final Logger logger = LoggerFactory.getLogger(RefreshTokenService.class);
    private static final SecureRandom secureRandom = new SecureRandom();

    private final RefreshTokenRepository refreshTokenRepository;
    private final AuthProperties authProperties;

    public RefreshTokenService(RefreshTokenRepository refreshTokenRepository, 
                             AuthProperties authProperties) {
        this.refreshTokenRepository = refreshTokenRepository;
        this.authProperties = authProperties;
    }

    /**
     * Creates a new refresh token for a user.
     *
     * @param userId the user ID
     * @param userAgent the user agent string
     * @return the created refresh token
     */
    public RefreshToken createRefreshToken(Long userId, String userAgent) {
        String token = generateSecureToken();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiresAt = now.plus(authProperties.getRefreshToken().getTtl());

        RefreshToken refreshToken = new RefreshToken();
        refreshToken.setToken(token);
        refreshToken.setUserId(userId);
        refreshToken.setIssuedAt(now);
        refreshToken.setExpiresAt(expiresAt);
        refreshToken.setUserAgent(userAgent);
        refreshToken.setCreatedAt(LocalDateTime.now());

        refreshTokenRepository.insert(refreshToken);
        
        logger.debug("Created refresh token for user {} with expiry {}", userId, expiresAt);
        
        return refreshToken;
    }

    /**
     * Validates and retrieves a refresh token.
     * 
     * @param token the refresh token string
     * @return Optional containing the refresh token if valid
     */
    @Transactional(readOnly = true)
    public Optional<RefreshToken> validateRefreshToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.warn("Attempted to validate null or empty refresh token");
            return Optional.empty();
        }

        RefreshToken refreshToken = refreshTokenRepository.fetchOneByToken(token);
        
        if (refreshToken == null) {
            logger.warn("Refresh token not found or invalid: {}", maskToken(token));
            return Optional.empty();
        }

        if (!refreshToken.getExpiresAt().isAfter(LocalDateTime.now()) || refreshToken.getRevoked()) {
            logger.warn("Refresh token is expired or revoked for user {}: {}", 
                       refreshToken.getUserId(), maskToken(token));
            return Optional.empty();
        }

        logger.debug("Validated refresh token for user {}", refreshToken.getUserId());
        return Optional.of(refreshToken);
    }

    /**
     * Rotates a refresh token by creating a new one and revoking the old one.
     *
     * @param oldToken the current refresh token
     * @param userAgent the user agent string
     * @return the new refresh token
     */
    public RefreshToken rotateRefreshToken(RefreshToken oldToken, String userAgent) {
        if (!authProperties.getRefreshToken().isRotationEnabled()) {
            logger.debug("Refresh token rotation is disabled, returning existing token");
            return oldToken;
        }

        // Check if the token chain has exceeded absolute lifetime
        if (hasExceededAbsoluteLifetime(oldToken)) {
            logger.warn("Refresh token chain has exceeded absolute lifetime for user {}", 
                       oldToken.getUserId());
            revokeAllTokensForUser(oldToken.getUserId());
            throw new IllegalStateException("Refresh token chain has exceeded absolute lifetime");
        }

        // Create new token
        RefreshToken newToken = createRefreshToken(oldToken.getUserId(), userAgent);
        
        // Revoke old token and link to new one
        refreshTokenRepository.revokeToken(oldToken.getId(), newToken.getId());
        
        logger.debug("Rotated refresh token for user {} from {} to {}", 
                    oldToken.getUserId(), maskToken(oldToken.getToken()), maskToken(newToken.getToken()));
        
        return newToken;
    }

    /**
     * Revokes a specific refresh token.
     * 
     * @param token the refresh token to revoke
     */
    public void revokeRefreshToken(RefreshToken token) {
        refreshTokenRepository.revokeToken(token.getId(), null);
        logger.debug("Revoked refresh token for user {}: {}", 
                    token.getUserId(), maskToken(token.getToken()));
    }

    /**
     * Revokes all refresh tokens for a user.
     * 
     * @param userId the user ID
     */
    public void revokeAllTokensForUser(Long userId) {
        refreshTokenRepository.revokeAllTokensForUser(userId);
        logger.debug("Revoked all refresh tokens for user {}", userId);
    }



    /**
     * Generates a cryptographically secure random token.
     * 
     * @return the generated token string
     */
    private String generateSecureToken() {
        byte[] tokenBytes = new byte[authProperties.getRefreshToken().getTokenLength()];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }

    /**
     * Checks if a refresh token chain has exceeded the absolute lifetime.
     * 
     * @param token the refresh token to check
     * @return true if exceeded absolute lifetime
     */
    private boolean hasExceededAbsoluteLifetime(RefreshToken token) {
        LocalDateTime absoluteExpiry = token.getIssuedAt().plus(authProperties.getRefreshToken().getAbsoluteLifetime());
        return LocalDateTime.now().isAfter(absoluteExpiry);
    }

    /**
     * Masks a token for logging purposes to prevent token leakage.
     * 
     * @param token the token to mask
     * @return the masked token
     */
    private String maskToken(String token) {
        if (token == null || token.length() < 8) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }
}
