package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.invoice.converter.InvoiceConverter;
import com.collabhub.be.modules.invoice.converter.InvoiceListItemConverter;
import com.collabhub.be.modules.invoice.dto.*;
import org.jooq.generated.enums.InvoiceStatus;
import com.collabhub.be.modules.invoice.repository.InvoiceRepositoryImpl;
import com.collabhub.be.modules.invoice.repository.InvoiceItemRepositoryImpl;
import com.collabhub.be.modules.invoice.repository.InvoiceRecipientRepositoryImpl;
import com.collabhub.be.modules.invoice.repository.InvoiceDeliveryLogRepositoryImpl;
import com.collabhub.be.modules.brands.repository.BrandRepositoryImpl;
import org.jooq.generated.tables.pojos.Invoice;
import org.jooq.generated.tables.pojos.InvoiceItem;
import org.jooq.generated.tables.pojos.InvoiceRecipient;
import org.jooq.generated.tables.pojos.InvoiceDeliveryLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Service for managing invoices.
 * Handles business logic for invoice operations including CRUD, status management, and calculations.
 */
@Service
@Transactional
public class InvoiceService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceService.class);

    private final InvoiceRepositoryImpl invoiceRepository;
    private final InvoiceItemRepositoryImpl invoiceItemRepository;
    private final InvoiceRecipientRepositoryImpl invoiceRecipientRepository;
    private final InvoiceDeliveryLogRepositoryImpl deliveryLogRepository;
    private final BrandRepositoryImpl brandRepository;
    private final InvoiceConverter invoiceConverter;
    private final InvoiceListItemConverter invoiceListItemConverter;
    private final InvoiceCalculationService calculationService;
    private final InvoiceSnapshotService snapshotService;
    private final InvoiceValidationService validationService;

    public InvoiceService(InvoiceRepositoryImpl invoiceRepository,
                         InvoiceItemRepositoryImpl invoiceItemRepository,
                         InvoiceRecipientRepositoryImpl invoiceRecipientRepository,
                         InvoiceDeliveryLogRepositoryImpl deliveryLogRepository,
                         BrandRepositoryImpl brandRepository,
                         InvoiceConverter invoiceConverter,
                         InvoiceListItemConverter invoiceListItemConverter,
                         InvoiceCalculationService calculationService,
                         InvoiceSnapshotService snapshotService,
                         InvoiceValidationService validationService) {
        this.invoiceRepository = invoiceRepository;
        this.invoiceItemRepository = invoiceItemRepository;
        this.invoiceRecipientRepository = invoiceRecipientRepository;
        this.deliveryLogRepository = deliveryLogRepository;
        this.brandRepository = brandRepository;
        this.invoiceConverter = invoiceConverter;
        this.invoiceListItemConverter = invoiceListItemConverter;
        this.calculationService = calculationService;
        this.snapshotService = snapshotService;
        this.validationService = validationService;
    }

    /**
     * Retrieves all invoices for an account with pagination and filtering.
     *
     * @param accountId the account ID for multi-tenancy
     * @param status optional status filter
     * @param fromDate optional start date filter
     * @param toDate optional end date filter
     * @param pageRequest pagination information
     * @return page of invoice responses
     */
    @Transactional(readOnly = true)
    public PageResponse<InvoiceResponse> getAllInvoices(Long accountId, InvoiceStatus status, LocalDate fromDate, LocalDate toDate, PageRequest pageRequest) {
        logger.debug("Retrieving invoices for account: {} with filters - status: {}, fromDate: {}, toDate: {}",
                    accountId, status, fromDate, toDate);

        List<Invoice> invoices = invoiceRepository.findAllByAccountId(
                accountId, status, fromDate, toDate,
                pageRequest.getOffset(), pageRequest.getSize());

        long totalCount = invoiceRepository.countByAccountId(accountId, status, fromDate, toDate);

        List<InvoiceResponse> responses = invoices.stream()
                .map(this::buildInvoiceResponse)
                .toList();

        logger.debug("Retrieved {} invoices out of {} total for account: {}", responses.size(), totalCount, accountId);

        return PageResponse.of(responses, pageRequest, totalCount);
    }

    /**
     * Retrieves all invoices for an account with pagination and filtering (optimized for list views).
     * This method returns lightweight DTOs without items and uses bulk brand name fetching for recipient information.
     *
     * @param accountId the account ID for multi-tenancy
     * @param status optional status filter
     * @param fromDate optional start date filter
     * @param toDate optional end date filter
     * @param pageRequest pagination information
     * @return page of lightweight invoice list items
     */
    @Transactional(readOnly = true)
    public PageResponse<InvoiceListItemDto> getInvoiceListItems(Long accountId, InvoiceStatus status, LocalDate fromDate, LocalDate toDate, PageRequest pageRequest) {
        logger.debug("Retrieving invoice list items for account: {} with filters - status: {}, fromDate: {}, toDate: {}",
                    accountId, status, fromDate, toDate);

        List<Invoice> invoices = invoiceRepository.findAllByAccountId(
                accountId, status, fromDate, toDate,
                pageRequest.getOffset(), pageRequest.getSize());

        long totalCount = invoiceRepository.countByAccountId(accountId, status, fromDate, toDate);

        // Bulk fetch brand names for all recipient IDs to avoid N+1 problem
        List<Long> recipientIds = invoices.stream()
                .map(Invoice::getRecipientId)
                .filter(id -> id != null)
                .distinct()
                .toList();
        Map<Long, String> brandNamesById = brandRepository.findBrandNamesByIds(recipientIds, accountId);

        // Convert to lightweight DTOs with bulk brand name data
        List<InvoiceListItemDto> responses = invoiceListItemConverter.toListItems(invoices, brandNamesById);

        logger.debug("Retrieved {} invoice list items out of {} total for account: {}", responses.size(), totalCount, accountId);

        return PageResponse.of(responses, pageRequest, totalCount);
    }

    /**
     * Retrieves a specific invoice by ID.
     *
     * @param id the invoice ID
     * @param accountId the account ID for multi-tenancy
     * @return the invoice response
     * @throws NotFoundException if the invoice is not found
     */
    @Transactional(readOnly = true)
    public InvoiceResponse getInvoiceById(Long id, Long accountId) {
        logger.debug("Retrieving invoice with ID: {} for account: {}", id, accountId);

        Invoice invoice = invoiceRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> {
                    logger.warn("Invoice not found: ID={}, accountId={}", id, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                            InvoiceConstants.INVOICE_NOT_FOUND_MESSAGE);
                });

        logger.debug("Successfully retrieved invoice: {} for account: {}", invoice.getInvoiceNumber(), accountId);

        return buildInvoiceResponse(invoice);
    }

    /**
     * Creates a new invoice with items and recipients.
     *
     * @param request the create request
     * @param accountId the account ID for multi-tenancy
     * @return the created invoice response
     * @throws BadRequestException if validation fails
     */
    @Transactional
    public InvoiceResponse createInvoice(InvoiceCreateRequest request, Long accountId) {
        logger.debug("Creating new invoice for account: {}", accountId);

        // Validate the create request
        validationService.validateCreateRequest(request, accountId);

        // Create and persist the invoice
        Invoice invoice = createInvoiceEntity(request, accountId);

        // Create related entities
        createInvoiceItems(request.getItems(), invoice.getId());
        createInvoiceRecipients(request.getRecipients(), invoice.getId());

        logger.info("Successfully created invoice: {} for account: {}", invoice.getInvoiceNumber(), accountId);

        return buildInvoiceResponse(invoice);
    }

    /**
     * Creates and persists the invoice entity with calculated totals.
     */
    private Invoice createInvoiceEntity(InvoiceCreateRequest request, Long accountId) {
        // Convert request to entity
        Invoice invoice = invoiceConverter.toInvoice(request, accountId);

        // Set entity IDs (snapshots will be created only when status changes from DRAFT)
        invoice.setIssuerId(request.getIssuerId());
        invoice.setRecipientId(request.getRecipientId());
        invoice.setBankDetailsId(request.getBankDetailsId());

        // Calculate totals from items
        var calculationResult = calculationService.calculateTotalsFromRequests(request.getItems());
        calculationService.updateInvoiceWithTotals(invoice, calculationResult);

        // Create the invoice (status is DRAFT by default, no snapshots needed)
        invoiceRepository.insert(invoice);

        return invoice;
    }

    /**
     * Creates and persists invoice items.
     */
    private void createInvoiceItems(List<InvoiceItemRequest> itemRequests, Long invoiceId) {
        List<InvoiceItem> items = invoiceConverter.toInvoiceItems(itemRequests, invoiceId);
        invoiceItemRepository.batchInsert(items);
    }

    /**
     * Creates and persists invoice recipients.
     */
    private void createInvoiceRecipients(List<InvoiceRecipientRequest> recipientRequests, Long invoiceId) {
        List<InvoiceRecipient> recipients = invoiceConverter.toInvoiceRecipients(recipientRequests, invoiceId);
        invoiceRecipientRepository.batchInsert(recipients);
    }

    /**
     * Updates an existing invoice.
     *
     * @param id the invoice ID
     * @param request the update request
     * @param accountId the account ID for multi-tenancy
     * @return the updated invoice response
     * @throws NotFoundException if the invoice is not found
     * @throws BadRequestException if validation fails
     */
    @Transactional
    public InvoiceResponse updateInvoice(Long id, InvoiceUpdateRequest request, Long accountId) {
        logger.debug("Updating invoice with ID: {} for account: {}", id, accountId);

        // Find and validate existing invoice
        Invoice existingInvoice = findAndValidateInvoiceForUpdate(id, request, accountId);

        // Update invoice fields
        updateInvoiceFields(existingInvoice, request);

        // Update related entities if provided
        updateInvoiceItemsIfProvided(request.getItems(), existingInvoice, id);
        updateInvoiceRecipientsIfProvided(request.getRecipients(), id);

        // Persist changes
        invoiceRepository.update(existingInvoice);

        logger.info("Successfully updated invoice: {} for account: {}", existingInvoice.getInvoiceNumber(), accountId);

        return buildInvoiceResponse(existingInvoice);
    }

    /**
     * Finds and validates an invoice for update operations.
     */
    private Invoice findAndValidateInvoiceForUpdate(Long id, InvoiceUpdateRequest request, Long accountId) {
        Invoice existingInvoice = invoiceRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> {
                    logger.warn("Invoice not found for update: ID={}, accountId={}", id, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                            InvoiceConstants.INVOICE_NOT_FOUND_MESSAGE);
                });

        // Validate the update request (includes checking if invoice can be edited)
        validationService.validateUpdateRequest(request, existingInvoice, accountId);

        return existingInvoice;
    }

    /**
     * Updates invoice fields from the request.
     */
    private void updateInvoiceFields(Invoice existingInvoice, InvoiceUpdateRequest request) {
        invoiceConverter.updateInvoiceFromRequest(existingInvoice, request);
        existingInvoice.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * Updates invoice items if provided in the request.
     */
    private void updateInvoiceItemsIfProvided(List<InvoiceItemRequest> itemRequests, Invoice invoice, Long invoiceId) {
        if (itemRequests != null && !itemRequests.isEmpty()) {
            var calculationResult = calculationService.calculateTotalsFromRequests(itemRequests);
            calculationService.updateInvoiceWithTotals(invoice, calculationResult);

            // Update invoice items - delete all and re-insert
            invoiceItemRepository.deleteByInvoiceId(invoiceId);
            List<InvoiceItem> items = invoiceConverter.toInvoiceItems(itemRequests, invoiceId);
            invoiceItemRepository.batchInsert(items);
        }
    }

    /**
     * Updates invoice recipients if provided in the request.
     */
    private void updateInvoiceRecipientsIfProvided(List<InvoiceRecipientRequest> recipientRequests, Long invoiceId) {
        if (recipientRequests != null && !recipientRequests.isEmpty()) {
            invoiceRecipientRepository.deleteByInvoiceId(invoiceId);
            List<InvoiceRecipient> recipients = invoiceConverter.toInvoiceRecipients(recipientRequests, invoiceId);
            invoiceRecipientRepository.batchInsert(recipients);
        }
    }

    /**
     * Soft deletes an invoice.
     *
     * @param id the invoice ID
     * @param accountId the account ID for multi-tenancy
     * @throws NotFoundException if the invoice is not found
     */
    @Transactional
    public void deleteInvoice(Long id, Long accountId) {
        logger.debug("Soft deleting invoice with ID: {} for account: {}", id, accountId);

        boolean deleted = invoiceRepository.softDelete(id, accountId);

        if (!deleted) {
            logger.warn("Invoice not found for deletion: ID={}, accountId={}", id, accountId);
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    InvoiceConstants.INVOICE_NOT_FOUND_MESSAGE);
        }

        logger.info("Successfully soft deleted invoice with ID: {} for account: {}", id, accountId);
    }

    /**
     * Updates invoice status.
     *
     * @param id the invoice ID
     * @param status the new status
     * @param accountId the account ID for multi-tenancy
     * @return the updated invoice response
     * @throws NotFoundException if the invoice is not found
     */
    @Transactional
    public InvoiceResponse updateInvoiceStatus(Long id, InvoiceStatus status, Long accountId) {
        logger.debug("Updating status of invoice with ID: {} to {} for account: {}", id, status, accountId);

        // Find existing invoice to validate current status
        Invoice existingInvoice = invoiceRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> {
                    logger.warn("Invoice not found for status update: ID={}, accountId={}", id, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                            "Invoice not found or access denied");
                });

        InvoiceStatus currentStatus = existingInvoice.getStatus();

        // Validate status transition
        validationService.validateStatusTransition(currentStatus, status);

        // If transitioning from DRAFT, create snapshots before updating status
        boolean isTransitioningFromDraft = InvoiceStatus.draft.equals(currentStatus) && !InvoiceStatus.draft.equals(status);
        if (isTransitioningFromDraft) {
            logger.debug("Transitioning invoice {} from DRAFT to {}, creating snapshots", id, status);
            snapshotService.createSnapshots(existingInvoice,
                    existingInvoice.getIssuerId(),
                    existingInvoice.getRecipientId(),
                    existingInvoice.getBankDetailsId());

            // Update the invoice with snapshots
            invoiceRepository.update(existingInvoice);
        }

        // Update the status
        boolean updated = invoiceRepository.updateStatus(id, accountId, status);

        if (!updated) {
            logger.warn("Failed to update invoice status: ID={}, accountId={}", id, accountId);
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Invoice not found or access denied");
        }

        // Retrieve and return updated invoice
        Invoice updatedInvoice = invoiceRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                        "Invoice not found after status update"));

        logger.info("Successfully updated status of invoice: {} from {} to {} for account: {}",
                   updatedInvoice.getInvoiceNumber(), currentStatus, status, accountId);

        return buildInvoiceResponse(updatedInvoice);
    }

    /**
     * Generates the next invoice number for the account.
     * Analyzes the latest invoice number and increments it intelligently.
     *
     * @param accountId the account ID for multi-tenancy
     * @return the next invoice number response
     */
    @Transactional(readOnly = true)
    public NextInvoiceNumberResponse generateNextInvoiceNumber(Long accountId) {
        logger.debug("Generating next invoice number for account: {}", accountId);

        Optional<String> latestInvoiceNumber = invoiceRepository.findLatestInvoiceNumber(accountId);

        if (latestInvoiceNumber.isEmpty()) {
            // No invoices exist yet, return empty string as requested
            logger.debug("No existing invoices found for account: {}, returning empty string", accountId);
            return new NextInvoiceNumberResponse("", true, "empty");
        }

        String latest = latestInvoiceNumber.get();
        String nextNumber = incrementInvoiceNumber(latest);
        String pattern = detectPattern(latest);

        logger.debug("Generated next invoice number: {} from latest: {} for account: {}",
                    nextNumber, latest, accountId);

        return new NextInvoiceNumberResponse(nextNumber, true, pattern);
    }

    /**
     * Retrieves delivery logs for a specific invoice.
     *
     * @param id the invoice ID
     * @param accountId the account ID for multi-tenancy
     * @return list of delivery log responses
     * @throws NotFoundException if the invoice is not found
     */
    @Transactional(readOnly = true)
    public List<InvoiceDeliveryLogResponse> getInvoiceDeliveryLogs(Long id, Long accountId) {
        logger.debug("Retrieving delivery logs for invoice with ID: {} for account: {}", id, accountId);

        // Verify invoice exists and user has access (this will throw NotFoundException if not found)
        invoiceRepository.findByIdAndAccountId(id, accountId)
                .orElseThrow(() -> {
                    logger.warn("Invoice not found for delivery logs: ID={}, accountId={}", id, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                            "Invoice not found or access denied");
                });

        // Get delivery logs
        List<InvoiceDeliveryLog> deliveryLogs = deliveryLogRepository.findByInvoiceId(id);

        // Convert to response DTOs
        return deliveryLogs.stream()
                .map(this::convertToDeliveryLogResponse)
                .collect(Collectors.toList());
    }

    /**
     * Increments an invoice number intelligently.
     * Handles both numeric and character increments (e.g., 5 -> 6, A -> B, Z -> AA).
     *
     * @param invoiceNumber the current invoice number
     * @return the incremented invoice number
     */
    private String incrementInvoiceNumber(String invoiceNumber) {
        if (invoiceNumber == null || invoiceNumber.trim().isEmpty()) {
            return "1";
        }

        // Try to increment from the end of the string
        char[] chars = invoiceNumber.toCharArray();
        boolean incremented = false;

        // Start from the last character and work backwards
        for (int i = chars.length - 1; i >= 0 && !incremented; i--) {
            char currentChar = chars[i];

            if (Character.isDigit(currentChar)) {
                // Handle numeric increment
                if (currentChar == '9') {
                    chars[i] = '0';
                    // Continue to next position (carry over)
                } else {
                    chars[i] = (char) (currentChar + 1);
                    incremented = true;
                }
            } else if (Character.isLetter(currentChar)) {
                // Handle character increment
                if (Character.isUpperCase(currentChar)) {
                    if (currentChar == 'Z') {
                        chars[i] = 'A';
                        // Continue to next position (carry over)
                    } else {
                        chars[i] = (char) (currentChar + 1);
                        incremented = true;
                    }
                } else if (Character.isLowerCase(currentChar)) {
                    if (currentChar == 'z') {
                        chars[i] = 'a';
                        // Continue to next position (carry over)
                    } else {
                        chars[i] = (char) (currentChar + 1);
                        incremented = true;
                    }
                }
            } else {
                // Non-alphanumeric character, stop here and append increment
                break;
            }
        }

        String result = new String(chars);

        if (!incremented) {
            // All characters were at their maximum (e.g., "999" or "ZZZ")
            // Determine what type of character to prepend based on the last character
            char lastChar = invoiceNumber.charAt(invoiceNumber.length() - 1);
            if (Character.isDigit(lastChar)) {
                result = "1" + result;
            } else if (Character.isUpperCase(lastChar)) {
                result = "A" + result;
            } else if (Character.isLowerCase(lastChar)) {
                result = "a" + result;
            } else {
                // Fallback: append "1" for non-alphanumeric
                result = result + "1";
            }
        }

        return result;
    }

    /**
     * Detects the pattern of an invoice number for categorization.
     *
     * @param invoiceNumber the invoice number to analyze
     * @return a description of the detected pattern
     */
    private String detectPattern(String invoiceNumber) {
        if (invoiceNumber == null || invoiceNumber.trim().isEmpty()) {
            return "empty";
        }

        if (invoiceNumber.matches("^[0-9]+$")) {
            return "numeric";
        } else if (invoiceNumber.matches("^.*[0-9]+$")) {
            return "alphanumeric";
        } else {
            return "text";
        }
    }

    /**
     * Converts InvoiceDeliveryLog entity to response DTO.
     */
    private InvoiceDeliveryLogResponse convertToDeliveryLogResponse(InvoiceDeliveryLog log) {
        return new InvoiceDeliveryLogResponse(
                log.getId(),
                log.getInvoiceId(),
                log.getRecipientEmail(),
                log.getDeliveryStatus(),
                log.getErrorMessage(),
                log.getCreatedAt()
        );
    }

    /**
     * Builds a complete invoice response with items and recipients.
     *
     * @param invoice the invoice entity
     * @return the complete invoice response
     */
    private InvoiceResponse buildInvoiceResponse(Invoice invoice) {
        // Get related entities
        List<InvoiceItem> items = invoiceItemRepository.findByInvoiceId(invoice.getId());
        List<InvoiceRecipient> recipients = invoiceRecipientRepository.findByInvoiceId(invoice.getId());

        // Convert to response
        return invoiceConverter.toResponse(invoice, items, recipients);
    }
}
