package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.modules.collaborationhub.dto.BriefScopeDto;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service for handling brief access control logic.
 * Determines who can access briefs based on scope and participant roles.
 */
@Service
public class BriefAccessControlService {

    /**
     * Checks if a participant has access to a brief based on its scope and specific participants.
     *
     * @param scope the brief scope
     * @param specificParticipantIds list of specific participant IDs (for custom_selection scope)
     * @param participant the participant to check access for
     * @return true if the participant has access, false otherwise
     */
    public boolean hasAccessToBrief(BriefScopeDto scope, List<Long> specificParticipantIds, HubParticipant participant) {
        if (scope == null || participant == null) {
            return false;
        }

        switch (scope) {
            case ALL_PARTICIPANTS:
                return true;

            case ADMINS_REVIEWERS:
                return isAdminOr<PERSON><PERSON><PERSON><PERSON>(participant);

            case ADMINS_ONLY:
                return isAdmin(participant);

            case CUSTOM_SELECTION:
                // Admins and reviewers can always see custom briefs
                if (isAdminOrReviewer(participant)) {
                    return true;
                }
                // Check if participant is specifically included
                return specificParticipantIds != null && 
                       specificParticipantIds.contains(participant.getId());

            default:
                return false;
        }
    }

    /**
     * Checks if a participant can create briefs in a hub.
     * All participants can create briefs.
     *
     * @param participant the participant to check
     * @return true if the participant can create briefs
     */
    public boolean canCreateBrief(HubParticipant participant) {
        return participant != null;
    }

    /**
     * Checks if a participant can edit a brief.
     * Only the brief creator and hub admins can edit briefs.
     *
     * @param participant the participant to check
     * @param briefCreatorId the ID of the participant who created the brief
     * @return true if the participant can edit the brief
     */
    public boolean canEditBrief(HubParticipant participant, Long briefCreatorId) {
        if (participant == null || briefCreatorId == null) {
            return false;
        }

        // Brief creator can edit their own brief
        if (participant.getId().equals(briefCreatorId)) {
            return true;
        }

        // Hub admins can edit any brief
        return isAdmin(participant);
    }

    /**
     * Checks if a participant can delete a brief.
     * Only the brief creator and hub admins can delete briefs.
     *
     * @param participant the participant to check
     * @param briefCreatorId the ID of the participant who created the brief
     * @return true if the participant can delete the brief
     */
    public boolean canDeleteBrief(HubParticipant participant, Long briefCreatorId) {
        // Same logic as edit for now
        return canEditBrief(participant, briefCreatorId);
    }

    /**
     * Validates if the scope and specific participant IDs are consistent.
     *
     * @param scope the brief scope
     * @param specificParticipantIds list of specific participant IDs
     * @return true if the combination is valid
     */
    public boolean isValidScopeConfiguration(BriefScopeDto scope, List<Long> specificParticipantIds) {
        if (scope == null) {
            return false;
        }

        switch (scope) {
            case ALL_PARTICIPANTS:
            case ADMINS_REVIEWERS:
            case ADMINS_ONLY:
                // These scopes should not have specific participant IDs
                return specificParticipantIds == null || specificParticipantIds.isEmpty();

            case CUSTOM_SELECTION:
                // Custom selection should have at least one specific participant
                return specificParticipantIds != null && !specificParticipantIds.isEmpty();

            default:
                return false;
        }
    }



    private boolean isAdmin(HubParticipant participant) {
        return participant.getRole() == HubParticipantRole.admin;
    }

    private boolean isAdminOrReviewer(HubParticipant participant) {
        HubParticipantRole role = participant.getRole();
        return role == HubParticipantRole.admin || 
               role == HubParticipantRole.reviewer || 
               role == HubParticipantRole.reviewer_creator;
    }
}
