package com.collabhub.be.modules.posts.dto;

import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.jooq.generated.enums.ReviewStatus;

import java.util.List;

/**
 * Response DTO for listing posts with pagination and filtering.
 * Extends the generic PageResponse to include post-specific filters.
 */
public class PostListResponse extends PageResponse<PostListItemResponse> {

    private PostFilters filters;

    public PostListResponse() {
        super(List.of(), PageRequest.of(0), 0);
    }

    public PostListResponse(List<PostListItemResponse> content, PageRequest pageRequest,
                           long totalElements, PostFilters filters) {
        super(content, pageRequest, totalElements);
        this.filters = filters;
    }

    public PostFilters getFilters() {
        return filters;
    }

    public void setFilters(PostFilters filters) {
        this.filters = filters;
    }

    /**
     * Nested class representing available filters for posts.
     */
    public static class PostFilters {
        private List<String> available;
        private String current;
        private List<ReviewStatus> availableStatuses;
        private ReviewStatus currentStatus;

        public PostFilters() {}

        public PostFilters(List<String> available, String current, 
                          List<ReviewStatus> availableStatuses, ReviewStatus currentStatus) {
            this.available = available;
            this.current = current;
            this.availableStatuses = availableStatuses;
            this.currentStatus = currentStatus;
        }

        public List<String> getAvailable() {
            return available;
        }

        public void setAvailable(List<String> available) {
            this.available = available;
        }

        public String getCurrent() {
            return current;
        }

        public void setCurrent(String current) {
            this.current = current;
        }

        public List<ReviewStatus> getAvailableStatuses() {
            return availableStatuses;
        }

        public void setAvailableStatuses(List<ReviewStatus> availableStatuses) {
            this.availableStatuses = availableStatuses;
        }

        public ReviewStatus getCurrentStatus() {
            return currentStatus;
        }

        public void setCurrentStatus(ReviewStatus currentStatus) {
            this.currentStatus = currentStatus;
        }
    }

    @Override
    public String toString() {
        return "PostListResponse{" +
                "filters=" + filters +
                ", content=" + getContent() +
                ", page=" + getPage() +
                ", size=" + getSize() +
                ", totalElements=" + getTotalElements() +
                '}';
    }
}
