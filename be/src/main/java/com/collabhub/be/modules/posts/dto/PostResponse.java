package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.ReviewStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for post details.
 */
public class PostResponse {

    private Long id;
    private String caption;

    @JsonProperty("media_uris")
    private List<MediaItem> mediaUris;

    @JsonProperty("review_status")
    private ReviewStatus reviewStatus;

    @JsonProperty("reviewer_notes")
    private String reviewerNotes;

    private PostCreator creator;

    @JsonProperty("assigned_reviewers")
    private List<PostReviewer> assignedReviewers;

    private PostPermissions permissions;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    public PostResponse() {}

    public PostResponse(Long id, String caption, List<MediaItem> mediaUris, ReviewStatus reviewStatus,
                       String reviewerNotes, PostCreator creator, List<PostReviewer> assignedReviewers,
                       PostPermissions permissions, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.caption = caption;
        this.mediaUris = mediaUris;
        this.reviewStatus = reviewStatus;
        this.reviewerNotes = reviewerNotes;
        this.creator = creator;
        this.assignedReviewers = assignedReviewers;
        this.permissions = permissions;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public List<MediaItem> getMediaUris() {
        return mediaUris;
    }

    public void setMediaUris(List<MediaItem> mediaUris) {
        this.mediaUris = mediaUris;
    }

    public ReviewStatus getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(ReviewStatus reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReviewerNotes() {
        return reviewerNotes;
    }

    public void setReviewerNotes(String reviewerNotes) {
        this.reviewerNotes = reviewerNotes;
    }

    public PostCreator getCreator() {
        return creator;
    }

    public void setCreator(PostCreator creator) {
        this.creator = creator;
    }

    public List<PostReviewer> getAssignedReviewers() {
        return assignedReviewers;
    }

    public void setAssignedReviewers(List<PostReviewer> assignedReviewers) {
        this.assignedReviewers = assignedReviewers;
    }

    public PostPermissions getPermissions() {
        return permissions;
    }

    public void setPermissions(PostPermissions permissions) {
        this.permissions = permissions;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Nested class representing the post creator.
     */
    public static class PostCreator {
        private Long id;
        private String name;
        private String email;

        public PostCreator() {}

        public PostCreator(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    /**
     * Nested class representing a post reviewer.
     */
    public static class PostReviewer {
        private Long id;
        private String name;
        private String email;
        private ReviewStatus status;

        public PostReviewer() {}

        public PostReviewer(Long id, String name, String email, ReviewStatus status) {
            this.id = id;
            this.name = name;
            this.email = email;
            this.status = status;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public ReviewStatus getStatus() {
            return status;
        }

        public void setStatus(ReviewStatus status) {
            this.status = status;
        }
    }

    /**
     * Nested class representing user permissions for the post.
     */
    public static class PostPermissions {
        @JsonProperty("can_edit")
        private boolean canEdit;

        @JsonProperty("can_review")
        private boolean canReview;

        @JsonProperty("can_comment")
        private boolean canComment;

        @JsonProperty("can_assign_reviewers")
        private boolean canAssignReviewers;

        @JsonProperty("is_assigned_reviewer")
        private boolean isAssignedReviewer;

        @JsonProperty("has_reviewer_role")
        private boolean hasReviewerRole;

        @JsonProperty("my_review_status")
        private ReviewStatus myReviewStatus;

        @JsonProperty("user_role")
        private String userRole;

        public PostPermissions() {}

        public PostPermissions(boolean canEdit, boolean canReview, boolean canComment, boolean canAssignReviewers,
                             boolean isAssignedReviewer, boolean hasReviewerRole, ReviewStatus myReviewStatus, String userRole) {
            this.canEdit = canEdit;
            this.canReview = canReview;
            this.canComment = canComment;
            this.canAssignReviewers = canAssignReviewers;
            this.isAssignedReviewer = isAssignedReviewer;
            this.hasReviewerRole = hasReviewerRole;
            this.myReviewStatus = myReviewStatus;
            this.userRole = userRole;
        }

        public boolean isCanEdit() {
            return canEdit;
        }

        public void setCanEdit(boolean canEdit) {
            this.canEdit = canEdit;
        }

        public boolean isCanReview() {
            return canReview;
        }

        public void setCanReview(boolean canReview) {
            this.canReview = canReview;
        }

        public boolean isCanComment() {
            return canComment;
        }

        public void setCanComment(boolean canComment) {
            this.canComment = canComment;
        }

        public boolean isCanAssignReviewers() {
            return canAssignReviewers;
        }

        public void setCanAssignReviewers(boolean canAssignReviewers) {
            this.canAssignReviewers = canAssignReviewers;
        }

        public boolean isAssignedReviewer() {
            return isAssignedReviewer;
        }

        public void setAssignedReviewer(boolean assignedReviewer) {
            isAssignedReviewer = assignedReviewer;
        }

        public boolean isHasReviewerRole() {
            return hasReviewerRole;
        }

        public void setHasReviewerRole(boolean hasReviewerRole) {
            this.hasReviewerRole = hasReviewerRole;
        }

        public ReviewStatus getMyReviewStatus() {
            return myReviewStatus;
        }

        public void setMyReviewStatus(ReviewStatus myReviewStatus) {
            this.myReviewStatus = myReviewStatus;
        }

        public String getUserRole() {
            return userRole;
        }

        public void setUserRole(String userRole) {
            this.userRole = userRole;
        }
    }

    @Override
    public String toString() {
        return "PostResponse{" +
                "id=" + id +
                ", caption='" + caption + '\'' +
                ", mediaUris=" + mediaUris +
                ", reviewStatus=" + reviewStatus +
                ", reviewerNotes='" + reviewerNotes + '\'' +
                ", creator=" + creator +
                ", assignedReviewers=" + assignedReviewers +
                ", permissions=" + permissions +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
