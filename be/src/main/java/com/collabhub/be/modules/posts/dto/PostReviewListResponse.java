package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Response DTO for listing all reviews of a post.
 */
public class PostReviewListResponse {

    @JsonProperty("post_id")
    private Long postId;

    private List<PostReviewResponse> reviews;

    @JsonProperty("review_summary")
    private ReviewSummary reviewSummary;

    public PostReviewListResponse() {}

    public PostReviewListResponse(Long postId, List<PostReviewResponse> reviews, ReviewSummary reviewSummary) {
        this.postId = postId;
        this.reviews = reviews;
        this.reviewSummary = reviewSummary;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public List<PostReviewResponse> getReviews() {
        return reviews;
    }

    public void setReviews(List<PostReviewResponse> reviews) {
        this.reviews = reviews;
    }

    public ReviewSummary getReviewSummary() {
        return reviewSummary;
    }

    public void setReviewSummary(ReviewSummary reviewSummary) {
        this.reviewSummary = reviewSummary;
    }

    /**
     * Nested class representing review summary statistics.
     */
    public static class ReviewSummary {
        
        @JsonProperty("total_reviewers")
        private int totalReviewers;

        @JsonProperty("pending_reviews")
        private int pendingReviews;

        @JsonProperty("approved_reviews")
        private int approvedReviews;

        @JsonProperty("rework_reviews")
        private int reworkReviews;

        public ReviewSummary() {}

        public ReviewSummary(int totalReviewers, int pendingReviews, int approvedReviews, int reworkReviews) {
            this.totalReviewers = totalReviewers;
            this.pendingReviews = pendingReviews;
            this.approvedReviews = approvedReviews;
            this.reworkReviews = reworkReviews;
        }

        public int getTotalReviewers() {
            return totalReviewers;
        }

        public void setTotalReviewers(int totalReviewers) {
            this.totalReviewers = totalReviewers;
        }

        public int getPendingReviews() {
            return pendingReviews;
        }

        public void setPendingReviews(int pendingReviews) {
            this.pendingReviews = pendingReviews;
        }

        public int getApprovedReviews() {
            return approvedReviews;
        }

        public void setApprovedReviews(int approvedReviews) {
            this.approvedReviews = approvedReviews;
        }

        public int getReworkReviews() {
            return reworkReviews;
        }

        public void setReworkReviews(int reworkReviews) {
            this.reworkReviews = reworkReviews;
        }

        @Override
        public String toString() {
            return "ReviewSummary{" +
                    "totalReviewers=" + totalReviewers +
                    ", pendingReviews=" + pendingReviews +
                    ", approvedReviews=" + approvedReviews +
                    ", reworkReviews=" + reworkReviews +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "PostReviewListResponse{" +
                "postId=" + postId +
                ", reviews=" + reviews +
                ", reviewSummary=" + reviewSummary +
                '}';
    }
}
