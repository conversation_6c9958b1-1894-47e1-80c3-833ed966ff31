package com.collabhub.be.modules.collaborationhub.controller;

import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.util.CookieUtil;
import com.collabhub.be.modules.collaborationhub.dto.ExternalParticipantAuthResponse;
import com.collabhub.be.modules.collaborationhub.service.ExternalParticipantAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * REST controller for magic link authentication.
 * Handles external user authentication via magic links for collaboration hub access.
 * Generates JWT tokens and sets refresh token cookies following the same pattern as regular authentication.
 */
@RestController
@RequestMapping("/api/auth/magic-link")
@Tag(name = "Magic Link Authentication", description = "External user authentication via magic links")
public class MagicLinkAuthController {

    private static final Logger logger = LoggerFactory.getLogger(MagicLinkAuthController.class);

    private final ExternalParticipantAuthService externalParticipantAuthService;
    private final CookieUtil cookieUtil;
    private final AuthProperties authProperties;

    public MagicLinkAuthController(ExternalParticipantAuthService externalParticipantAuthService,
                                 CookieUtil cookieUtil,
                                 AuthProperties authProperties) {
        this.externalParticipantAuthService = externalParticipantAuthService;
        this.cookieUtil = cookieUtil;
        this.authProperties = authProperties;
    }

    /**
     * Validates a magic link token and authenticates the external participant.
     * Generates JWT access and refresh tokens, sets refresh token as HTTP-only cookie.
     */
    @GetMapping("/{token}")
    @Operation(summary = "Authenticate via magic link",
               description = "Validates magic link token, generates JWT tokens, and authenticates external participant")
    public ResponseEntity<ExternalParticipantAuthResponse> authenticateWithMagicLink(
            @Parameter(description = "Magic link token", required = true)
            @PathVariable String token,
            HttpServletRequest request,
            HttpServletResponse response) {

        logger.info("Authenticating external participant with magic link");

        // Extract user agent for refresh token
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            userAgent = "Unknown";
        }

        // Authenticate and generate tokens
        ExternalParticipantAuthService.ExternalAuthenticationResult authResult =
                externalParticipantAuthService.authenticateWithMagicLink(token, userAgent);

        // Set refresh token as HTTP-only cookie
        cookieUtil.addRefreshTokenCookie(response, authResult.getRefreshToken());

        // Create response DTO
        ExternalParticipantAuthResponse authResponse = createExternalAuthResponse(authResult);

        logger.info("Successfully authenticated external participant {} for hub {}",
                   authResult.getParticipant().getId(), authResult.getHub().getId());

        return ResponseEntity.ok(authResponse);
    }

    /**
     * Refreshes tokens for an external participant using refresh token from cookie.
     */
    @PostMapping("/refresh")
    @Operation(summary = "Refresh external participant tokens",
               description = "Refreshes access token for external participant using refresh token cookie")
    public ResponseEntity<ExternalParticipantAuthResponse> refreshExternalParticipantToken(
            HttpServletRequest request,
            HttpServletResponse response) {

        logger.debug("Refreshing external participant token");

        // Extract refresh token from cookie
        String refreshToken = cookieUtil.getRefreshTokenFromCookies(request)
                .orElseThrow(() -> new IllegalArgumentException("Refresh token cookie not found"));

        // Extract user agent
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            userAgent = "Unknown";
        }

        // Refresh tokens
        ExternalParticipantAuthService.ExternalAuthenticationResult authResult =
                externalParticipantAuthService.refreshExternalParticipantToken(refreshToken, userAgent);

        // Set new refresh token cookie
        cookieUtil.addRefreshTokenCookie(response, authResult.getRefreshToken());

        // Create response DTO
        ExternalParticipantAuthResponse authResponse = createExternalAuthResponse(authResult);

        logger.info("Successfully refreshed token for external participant {}",
                   authResult.getParticipant().getId());

        return ResponseEntity.ok(authResponse);
    }

    /**
     * Creates an external participant authentication response from the authentication result.
     */
    private ExternalParticipantAuthResponse createExternalAuthResponse(
            ExternalParticipantAuthService.ExternalAuthenticationResult authResult) {

        // Extract display name from email
        String displayName = extractDisplayName(authResult.getParticipant().getEmail());

        // Create participant info
        ExternalParticipantAuthResponse.ParticipantInfo participantInfo =
                new ExternalParticipantAuthResponse.ParticipantInfo(
                        authResult.getParticipant().getId(),
                        authResult.getParticipant().getEmail(),
                        displayName,
                        authResult.getParticipant().getRole(),
                        authResult.getParticipant().getIsExternal()
                );

        // Create hub info
        ExternalParticipantAuthResponse.HubInfo hubInfo =
                new ExternalParticipantAuthResponse.HubInfo(
                        authResult.getHub().getId(),
                        authResult.getHub().getName(),
                        authResult.getHub().getAccountId(),
                        authResult.getHub().getBrandId()
                );

        // Get token expiry in seconds
        long expiresInSeconds = authProperties.getJwt().getAccessTokenTtl().toSeconds();

        return new ExternalParticipantAuthResponse(
                authResult.getAccessToken(),
                expiresInSeconds,
                participantInfo,
                hubInfo,
                authResult.isFirstAccess()
        );
    }

    /**
     * Extracts display name from participant email.
     */
    private String extractDisplayName(String email) {
        if (email != null && email.contains("@")) {
            return email.substring(0, email.indexOf('@'));
        }
        return email;
    }
}
