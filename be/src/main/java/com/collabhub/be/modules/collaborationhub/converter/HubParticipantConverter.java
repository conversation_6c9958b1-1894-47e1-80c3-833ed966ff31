package com.collabhub.be.modules.collaborationhub.converter;

import com.collabhub.be.modules.collaborationhub.dto.HubParticipantResponse;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Converter class for mapping between HubParticipant DTOs and jOOQ POJOs.
 * Handles conversion between different representations of hub participant data.
 */
@Component
public class HubParticipantConverter {

    /**
     * Creates a new hub participant POJO for an internal user.
     *
     * @param hubId the hub ID
     * @param userId the user ID
     * @param email the user email
     * @param role the participant role
     * @return the jOOQ POJO ready for database insertion
     */
    public HubParticipant createInternalParticipant(Long hubId, Long userId, String email, HubParticipantRole role) {
        HubParticipant participant = new HubParticipant();
        participant.setHubId(hubId);
        participant.setUserId(userId);
        participant.setEmail(email);
        participant.setRole(role);
        participant.setIsExternal(false);
        participant.setInvitedAt(LocalDateTime.now());
        participant.setJoinedAt(LocalDateTime.now()); // Internal users join immediately
        participant.setCreatedAt(LocalDateTime.now());
        
        return participant;
    }

    /**
     * Creates a new hub participant POJO for an external user.
     *
     * @param hubId the hub ID
     * @param email the external user email
     * @param role the participant role
     * @param magicLinkToken the magic link token for external access
     * @param magicLinkExpiry the expiry time for the magic link
     * @return the jOOQ POJO ready for database insertion
     */
    public HubParticipant createExternalParticipant(Long hubId, String email, HubParticipantRole role, 
                                                   String magicLinkToken, LocalDateTime magicLinkExpiry) {
        HubParticipant participant = new HubParticipant();
        participant.setHubId(hubId);
        participant.setUserId(null); // External users don't have user IDs
        participant.setEmail(email);
        participant.setRole(role);
        participant.setIsExternal(true);
        participant.setMagicLinkToken(magicLinkToken);
        participant.setMagicLinkExpiry(magicLinkExpiry);
        participant.setInvitedAt(LocalDateTime.now());
        participant.setJoinedAt(null); // External users join when they click the magic link
        participant.setCreatedAt(LocalDateTime.now());
        
        return participant;
    }

    /**
     * Converts a jOOQ POJO to a response DTO for API responses.
     *
     * @param participant the jOOQ POJO
     * @param userName the user name (from User table or external name)
     * @return the response DTO
     */
    public HubParticipantResponse toResponse(HubParticipant participant, String userName) {
        if (participant == null) {
            return null;
        }

        String status = determineParticipantStatus(participant);
        return new HubParticipantResponse(
                participant.getId(),
                participant.getUserId(),
                participant.getEmail(),
                userName,
                participant.getRole(),
                participant.getIsExternal(),
                participant.getInvitedAt(),
                participant.getJoinedAt(),
                status
        );
    }

    /**
     * Determines participant status based on their state.
     *
     * @param participant the participant
     * @return status string ("active", "pending", "removed")
     */
    private String determineParticipantStatus(HubParticipant participant) {
        if (participant.getRemovedAt() != null) {
            return "removed";
        }
        if (participant.getIsExternal() && participant.getJoinedAt() == null) {
            return "pending";
        }
        return "active";
    }

    /**
     * Soft deletes a participant by setting the removed_at timestamp.
     *
     * @param participant the participant to remove
     * @return the updated participant
     */
    public HubParticipant removeParticipant(HubParticipant participant) {
        if (participant == null) {
            return null;
        }

        participant.setRemovedAt(LocalDateTime.now());
        return participant;
    }
}
