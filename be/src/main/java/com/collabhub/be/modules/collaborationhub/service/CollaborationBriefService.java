package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.converter.CollaborationBriefConverter;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationBriefRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.jooq.Record;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationBrief;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing collaboration briefs within collaboration hubs.
 * Provides business logic for brief operations with proper access control and validation.
 */
@Service
@Transactional
public class CollaborationBriefService {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationBriefService.class);

    private final CollaborationBriefRepositoryImpl briefRepository;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationBriefConverter briefConverter;

    public CollaborationBriefService(CollaborationBriefRepositoryImpl briefRepository,
                                   CollaborationHubRepositoryImpl hubRepository,
                                   HubParticipantRepositoryImpl participantRepository,
                                   CollaborationBriefConverter briefConverter) {
        this.briefRepository = briefRepository;
        this.hubRepository = hubRepository;
        this.participantRepository = participantRepository;
        this.briefConverter = briefConverter;
    }

    /**
     * Creates a new collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param request the brief creation request
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID creating the brief
     * @return the created brief response
     */
    public CollaborationBriefResponse createBrief(Long hubId, CollaborationBriefCreateRequest request, 
                                                 Long accountId, Long userId) {
        logger.info("Creating brief '{}' in hub {} for account {} by user {}", 
                   request.getTitle(), hubId, accountId, userId);

        // Validate hub access and get participant
        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, accountId, userId);
        
        // Validate brief creation permissions
        validateBriefCreationPermissions(participant);

        // Check if brief title already exists in the hub
        if (briefRepository.existsByTitleAndHubId(request.getTitle(), hubId, accountId, null)) {
            throw new ConflictException(ErrorCode.BRIEF_TITLE_ALREADY_EXISTS, 
                "Brief with title '" + request.getTitle() + "' already exists in this hub");
        }

        // Create and save the brief
        CollaborationBrief brief = briefConverter.toEntity(request, hubId, participant.getId());
        briefRepository.insert(brief);

        logger.info("Created brief {} in hub {} for account {}", brief.getId(), hubId, accountId);

        // Return response with creator information
        String creatorName = getParticipantDisplayName(participant);
        return briefConverter.toResponse(brief, creatorName);
    }

    /**
     * Updates an existing collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param request the brief update request
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID updating the brief
     * @return the updated brief response
     */
    public CollaborationBriefResponse updateBrief(Long hubId, Long briefId, CollaborationBriefUpdateRequest request,
                                                 Long accountId, Long userId) {
        logger.info("Updating brief {} in hub {} for account {} by user {}", 
                   briefId, hubId, accountId, userId);

        // Validate hub access and get participant
        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, accountId, userId);

        // Find the brief
        CollaborationBrief brief = briefRepository.findByIdAndHubIdAndAccountId(briefId, hubId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.BRIEF_NOT_FOUND, 
                    "Brief not found with ID: " + briefId));

        // Validate update permissions
        validateBriefUpdatePermissions(participant, brief);

        // Check if title change conflicts with existing briefs
        if (!brief.getTitle().equals(request.getTitle()) && 
            briefRepository.existsByTitleAndHubId(request.getTitle(), hubId, accountId, briefId)) {
            throw new ConflictException(ErrorCode.BRIEF_TITLE_ALREADY_EXISTS, 
                "Brief with title '" + request.getTitle() + "' already exists in this hub");
        }

        // Update the brief
        CollaborationBrief updatedBrief = briefConverter.updateEntity(brief, request);
        briefRepository.update(updatedBrief);

        logger.info("Updated brief {} in hub {} for account {}", briefId, hubId, accountId);

        // Return response with creator information
        String creatorName = getParticipantDisplayName(participant);
        return briefConverter.toResponse(updatedBrief, creatorName);
    }

    /**
     * Retrieves a specific brief by ID.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID requesting the brief
     * @return the brief response
     */
    @Transactional(readOnly = true)
    public CollaborationBriefResponse getBriefById(Long hubId, Long briefId, Long accountId, Long userId) {
        logger.debug("Retrieving brief {} in hub {} for account {} by user {}", 
                    briefId, hubId, accountId, userId);

        // Validate hub access
        validateHubAccessAndGetParticipant(hubId, accountId, userId);

        // Find the brief with creator information
        Record record = briefRepository.findBriefWithCreatorInfo(briefId, hubId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.BRIEF_NOT_FOUND, 
                    "Brief not found with ID: " + briefId));

        CollaborationBrief brief = record.into(CollaborationBrief.class);
        String creatorName = extractCreatorName(record);

        return briefConverter.toResponse(brief, creatorName);
    }

    /**
     * Retrieves a paginated list of briefs for a collaboration hub.
     *
     * @param hubId the collaboration hub ID
     * @param pageRequest the pagination request
     * @param titleFilter optional title filter
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID requesting the briefs
     * @return paginated list of brief list items
     */
    @Transactional(readOnly = true)
    public PageResponse<CollaborationBriefListItemDto> getBriefs(Long hubId, PageRequest pageRequest, 
                                                               String titleFilter, Long accountId, Long userId) {
        logger.debug("Retrieving briefs for hub {} with filter '{}', page {}, size {} for account {} by user {}", 
                    hubId, titleFilter, pageRequest.getPage(), pageRequest.getSize(), accountId, userId);

        // Validate hub access
        validateHubAccessAndGetParticipant(hubId, accountId, userId);

        // Get total count for pagination metadata
        long totalCount = briefRepository.countBriefsWithFilter(hubId, accountId, titleFilter);

        // Get briefs with creator information for current page
        List<Record> records = briefRepository.findBriefsWithCreatorInfo(
                hubId, accountId, titleFilter, pageRequest.getOffset(), pageRequest.getSize());

        List<CollaborationBriefListItemDto> briefListItems = records.stream()
                .map(record -> {
                    CollaborationBrief brief = record.into(CollaborationBrief.class);
                    String creatorName = extractCreatorName(record);
                    return briefConverter.toListItem(brief, creatorName);
                })
                .collect(Collectors.toList());

        logger.debug("Retrieved {} briefs out of {} total for hub {} in account {}", 
                    briefListItems.size(), totalCount, hubId, accountId);

        return PageResponse.of(briefListItems, pageRequest, totalCount);
    }

    /**
     * Deletes a collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID deleting the brief
     */
    public void deleteBrief(Long hubId, Long briefId, Long accountId, Long userId) {
        logger.info("Deleting brief {} in hub {} for account {} by user {}",
                   briefId, hubId, accountId, userId);

        // Validate hub access and get participant
        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, accountId, userId);

        // Find the brief
        CollaborationBrief brief = briefRepository.findByIdAndHubIdAndAccountId(briefId, hubId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.BRIEF_NOT_FOUND,
                    "Brief not found with ID: " + briefId));

        // Validate delete permissions
        validateBriefDeletePermissions(participant, brief);

        // Delete the brief
        briefRepository.deleteById(briefId);

        logger.info("Deleted brief {} in hub {} for account {}", briefId, hubId, accountId);
    }

    /**
     * Validates hub access and returns the participant for the user.
     */
    private HubParticipant validateHubAccessAndGetParticipant(Long hubId, Long accountId, Long userId) {
        // Validate hub exists and user has access
        CollaborationHub hub = hubRepository.findByIdWithAccess(hubId, accountId, userId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_NOT_FOUND,
                    "Collaboration hub not found or access denied"));

        // Get the participant
        HubParticipant participant = participantRepository.findByHubIdAndUserId(hubId, userId);
        if (participant == null) {
            throw new ForbiddenException(ErrorCode.HUB_ACCESS_DENIED,
                "User is not a participant in this collaboration hub");
        }
        return participant;
    }

    /**
     * Validates that a participant can create briefs.
     */
    private void validateBriefCreationPermissions(HubParticipant participant) {
        // All participants can create briefs
        // Additional access control can be implemented via access_tags
        logger.debug("Brief creation allowed for participant {} with role {}",
                    participant.getId(), participant.getRole());
    }

    /**
     * Validates that a participant can update a specific brief.
     */
    private void validateBriefUpdatePermissions(HubParticipant participant, CollaborationBrief brief) {
        // Brief creators and hub admins can update briefs
        if (!brief.getCreatedByParticipantId().equals(participant.getId()) &&
            !HubParticipantRole.admin.equals(participant.getRole())) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED,
                "Only brief creators and hub admins can update briefs");
        }
    }

    /**
     * Validates that a participant can delete a specific brief.
     */
    private void validateBriefDeletePermissions(HubParticipant participant, CollaborationBrief brief) {
        // Brief creators and hub admins can delete briefs
        if (!brief.getCreatedByParticipantId().equals(participant.getId()) &&
            !HubParticipantRole.admin.equals(participant.getRole())) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED,
                "Only brief creators and hub admins can delete briefs");
        }
    }

    /**
     * Gets the display name for a participant.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        // For now, return email. This could be enhanced to include user names
        return participant.getEmail();
    }

    /**
     * Extracts creator name from a joined record.
     */
    private String extractCreatorName(Record record) {
        String displayName = record.get("creator_display_name", String.class);
        String email = record.get("creator_email", String.class);

        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        } else {
            return email != null ? email : "Unknown";
        }
    }
}
