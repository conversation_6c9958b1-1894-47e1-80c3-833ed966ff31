package com.collabhub.be.modules.posts.service;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.converter.PostReviewConverter;
import com.collabhub.be.modules.posts.dto.PostReviewCreateRequest;
import com.collabhub.be.modules.posts.dto.PostReviewListResponse;
import com.collabhub.be.modules.posts.dto.PostReviewResponse;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostReviewerRepositoryImpl;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.PostReviewer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service layer for Post Review operations.
 * Handles business logic, validation, and multi-tenancy for post reviews.
 */
@Service
@Transactional
public class PostReviewService {

    private static final Logger logger = LoggerFactory.getLogger(PostReviewService.class);

    private final PostReviewerRepositoryImpl postReviewerRepository;
    private final PostRepositoryImpl postRepository;
    private final HubParticipantRepositoryImpl hubParticipantRepository;
    private final PostReviewConverter postReviewConverter;

    public PostReviewService(PostReviewerRepositoryImpl postReviewerRepository,
                           PostRepositoryImpl postRepository,
                           HubParticipantRepositoryImpl hubParticipantRepository,
                           PostReviewConverter postReviewConverter) {
        this.postReviewerRepository = postReviewerRepository;
        this.postRepository = postRepository;
        this.hubParticipantRepository = hubParticipantRepository;
        this.postReviewConverter = postReviewConverter;
    }

    /**
     * Creates or updates a review for a post (upsert behavior).
     * Validates reviewer permissions and updates post status accordingly.
     */
    @Transactional
    public PostReviewResponse createOrUpdateReview(Long postId, PostReviewCreateRequest request, 
                                                  Long accountId, Long userId) {
        logger.debug("Creating/updating review for post {} by user {}", postId, userId);

        // Validate post exists and get hub information
        Post post = validatePostExists(postId, accountId, userId);
        
        // Validate user is a participant and has reviewer permissions
        HubParticipant reviewer = validateReviewerPermissions(post.getHubId(), userId, accountId);
        
        // Validate user is assigned as reviewer for this post
        validateReviewerAssignment(postId, reviewer.getId());

        // Create or update the review
        PostReviewer postReviewer = postReviewConverter.toPostReviewer(request, postId, reviewer.getId());
        PostReviewer savedReviewer = postReviewerRepository.upsertPostReviewer(postReviewer);

        // Update post status based on all reviews
        updatePostStatusBasedOnReviews(postId);

        logger.info("Successfully created/updated review for post {} by reviewer {}", postId, reviewer.getId());
        
        return postReviewConverter.toPostReviewResponse(savedReviewer, reviewer);
    }

    /**
     * Gets all reviews for a post.
     * Only accessible by participants of the collaboration hub.
     */
    @Transactional(readOnly = true)
    public PostReviewListResponse getPostReviews(Long postId, Long accountId, Long userId) {
        logger.debug("Retrieving reviews for post {} by user {}", postId, userId);

        // Validate post exists and user has access
        Post post = validatePostExists(postId, accountId, userId);

        // Get reviews with participant details (avoiding N+1 queries)
        List<PostReviewerRepositoryImpl.PostReviewerWithParticipant> reviewsWithParticipants = 
                postReviewerRepository.findReviewsWithParticipantsByPostId(postId);

        // Convert to response DTOs
        List<PostReviewResponse> reviewResponses = convertToReviewResponses(reviewsWithParticipants);

        logger.debug("Retrieved {} reviews for post {}", reviewResponses.size(), postId);
        
        return postReviewConverter.toPostReviewListResponse(postId, reviewResponses);
    }

    /**
     * Gets the current user's review for a post.
     * Returns null if no review exists.
     */
    @Transactional(readOnly = true)
    public PostReviewResponse getMyReview(Long postId, Long accountId, Long userId) {
        logger.debug("Retrieving user's review for post {} by user {}", postId, userId);

        // Validate post exists and user has access
        Post post = validatePostExists(postId, accountId, userId);
        HubParticipant participant = validateUserHubAccess(post.getHubId(), userId, accountId);

        // Find user's review
        Optional<PostReviewer> review = postReviewerRepository.findByPostIdAndParticipantId(postId, participant.getId());
        
        if (review.isEmpty()) {
            logger.debug("No review found for post {} by user {}", postId, userId);
            return null;
        }

        logger.debug("Retrieved review for post {} by user {}", postId, userId);
        return postReviewConverter.toPostReviewResponse(review.get(), participant);
    }

    /**
     * Validates that a post exists and user has access to it.
     */
    private Post validatePostExists(Long postId, Long accountId, Long userId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE);
        }

        // Validate user can access the post (includes account validation)
        if (!postRepository.canUserAccessPost(postId, userId, accountId)) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, PostConstants.ACCESS_DENIED_MESSAGE);
        }

        return post;
    }

    /**
     * Validates that a user is a participant in the hub and has reviewer permissions.
     */
    private HubParticipant validateReviewerPermissions(Long hubId, Long userId, Long accountId) {
        HubParticipant participant = hubParticipantRepository.findByHubIdAndUserId(hubId, userId);

        if (participant == null) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, PostConstants.USER_NOT_PARTICIPANT_MESSAGE);
        }

        // Check if participant has reviewer role
        if (participant.getRole() != HubParticipantRole.admin &&
            participant.getRole() != HubParticipantRole.reviewer &&
            participant.getRole() != HubParticipantRole.reviewer_creator) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, PostConstants.CANNOT_REVIEW_POST_MESSAGE);
        }

        return participant;
    }

    /**
     * Validates that a user has access to a hub (is a participant).
     */
    private HubParticipant validateUserHubAccess(Long hubId, Long userId, Long accountId) {
        HubParticipant participant = hubParticipantRepository.findByHubIdAndUserId(hubId, userId);

        if (participant == null) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, PostConstants.USER_NOT_PARTICIPANT_MESSAGE);
        }

        return participant;
    }

    /**
     * Validates that a user is assigned as a reviewer for a specific post.
     */
    private void validateReviewerAssignment(Long postId, Long participantId) {
        if (!postReviewerRepository.isParticipantAssignedAsReviewer(postId, participantId)) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, PostConstants.NOT_ASSIGNED_REVIEWER_MESSAGE);
        }
    }

    /**
     * Updates the post's overall status based on all reviewer decisions.
     */
    private void updatePostStatusBasedOnReviews(Long postId) {
        // Get all reviews for the post
        List<PostReviewerRepositoryImpl.PostReviewerWithParticipant> reviewsWithParticipants = 
                postReviewerRepository.findReviewsWithParticipantsByPostId(postId);
        
        List<PostReviewResponse> reviewResponses = convertToReviewResponses(reviewsWithParticipants);
        
        // Calculate new post status
        ReviewStatus newStatus = postReviewConverter.calculatePostStatus(reviewResponses);
        
        // Update post status
        boolean updated = postRepository.updateReviewStatus(postId, newStatus);
        
        if (updated) {
            logger.debug("Updated post {} status to {}", postId, newStatus);
        } else {
            logger.warn("Failed to update post {} status", postId);
        }
    }

    /**
     * Converts repository results to response DTOs.
     */
    private List<PostReviewResponse> convertToReviewResponses(
            List<PostReviewerRepositoryImpl.PostReviewerWithParticipant> reviewsWithParticipants) {
        
        List<PostReviewResponse> responses = new ArrayList<>();
        
        for (PostReviewerRepositoryImpl.PostReviewerWithParticipant rwp : reviewsWithParticipants) {
            // Create PostReviewer from the combined data
            PostReviewer postReviewer = new PostReviewer();
            postReviewer.setId(rwp.getId());
            postReviewer.setPostId(rwp.getPostId());
            postReviewer.setParticipantId(rwp.getParticipantId());
            postReviewer.setAssignedAt(rwp.getAssignedAt());
            postReviewer.setReviewStatus(rwp.getReviewStatus());
            postReviewer.setReviewNotes(rwp.getReviewNotes());
            postReviewer.setReviewedAt(rwp.getReviewedAt());

            // Create HubParticipant from the combined data
            HubParticipant participant = new HubParticipant();
            participant.setId(rwp.getParticipantId());
            participant.setEmail(rwp.getEmail());
            participant.setName(rwp.getName());
            participant.setIsExternal(rwp.getIsExternal());

            // Convert to response DTO
            PostReviewResponse response = postReviewConverter.toPostReviewResponse(postReviewer, participant);
            responses.add(response);
        }
        
        return responses;
    }
}
