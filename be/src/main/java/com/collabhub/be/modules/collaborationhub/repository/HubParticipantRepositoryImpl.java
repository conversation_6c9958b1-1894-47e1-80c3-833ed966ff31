package com.collabhub.be.modules.collaborationhub.repository;

import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.jooq.generated.tables.daos.HubParticipantDao;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.tables.HubParticipant.HUB_PARTICIPANT;
import static org.jooq.generated.tables.User.USER;

/**
 * Repository for HubParticipant entity using jOOQ for database operations.
 * Provides type-safe database operations for hub participant management.
 */
@Repository
public class HubParticipantRepositoryImpl extends HubParticipantDao {

    private final DSLContext dsl;

    public HubParticipantRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all active participants for a hub.
     *
     * @param hubId the hub ID
     * @return list of active participants
     */
    public List<HubParticipant> findActiveParticipantsByHubId(Long hubId) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .orderBy(HUB_PARTICIPANT.CREATED_AT.asc())
                .fetchInto(HubParticipant.class);
    }

    /**
     * Finds all participants for a hub (including removed ones).
     *
     * @param hubId the hub ID
     * @return list of all participants
     */
    public List<HubParticipant> findByHubId(Long hubId) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .orderBy(HUB_PARTICIPANT.CREATED_AT.asc())
                .fetchInto(HubParticipant.class);
    }

    /**
     * Counts active participants for a hub.
     *
     * @param hubId the hub ID
     * @return the number of active participants
     */
    public int countActiveParticipantsByHubId(Long hubId) {
        return dsl.selectCount()
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOne(0, Integer.class);
    }

    /**
     * Counts active participants for a hub with a specific role.
     *
     * @param hubId the hub ID
     * @param role the participant role
     * @return the number of active participants with the specified role
     */
    public long countActiveParticipantsByRole(Long hubId, org.jooq.generated.enums.HubParticipantRole role) {
        return dsl.selectCount()
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .and(HUB_PARTICIPANT.ROLE.eq(role))
                .fetchOne(0, Long.class);
    }

    /**
     * Finds participant names by their IDs for bulk operations.
     * Used for optimizing participant display in hub details.
     *
     * @param participantIds the list of participant IDs
     * @return map of participant ID to user name
     */
    public Map<Long, String> findParticipantNamesByIds(List<Long> participantIds) {
        if (participantIds == null || participantIds.isEmpty()) {
            return Map.of();
        }

        return dsl.select(HUB_PARTICIPANT.ID, USER.DISPLAY_NAME)
                .from(HUB_PARTICIPANT)
                .leftJoin(USER).on(USER.ID.eq(HUB_PARTICIPANT.USER_ID))
                .where(HUB_PARTICIPANT.ID.in(participantIds))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchMap(HUB_PARTICIPANT.ID, USER.DISPLAY_NAME);
    }

    /**
     * Bulk fetch participant details with user information for post creators.
     * Optimizes N+1 query problems when loading multiple posts.
     *
     * @param participantIds the list of participant IDs
     * @return map of participant ID to participant details
     */
    public Map<Long, ParticipantDetails> findParticipantDetailsByIds(List<Long> participantIds) {
        if (participantIds == null || participantIds.isEmpty()) {
            return Map.of();
        }

        var projections = dsl.select(
                        HUB_PARTICIPANT.ID,
                        HUB_PARTICIPANT.USER_ID,
                        HUB_PARTICIPANT.EMAIL,
                        HUB_PARTICIPANT.NAME,
                        HUB_PARTICIPANT.IS_EXTERNAL,
                        USER.DISPLAY_NAME.as("user_name"),
                        USER.EMAIL.as("user_email")
                )
                .from(HUB_PARTICIPANT)
                .leftJoin(USER).on(USER.ID.eq(HUB_PARTICIPANT.USER_ID))
                .where(HUB_PARTICIPANT.ID.in(participantIds))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchInto(ParticipantDetailsProjection.class);

        Map<Long, ParticipantDetails> result = new HashMap<>();
        for (var projection : projections) {
            String displayName = getDisplayNameFromProjection(projection);
            String email = getEmailFromProjection(projection);

            result.put(projection.getId(), new ParticipantDetails(projection.getId(), displayName, email));
        }

        return result;
    }

    private String getDisplayNameFromProjection(ParticipantDetailsProjection projection) {
        // Priority: participant.name > user.name > email prefix > fallback
        String participantName = projection.getName();
        if (participantName != null && !participantName.trim().isEmpty()) {
            return participantName;
        }

        String userName = projection.getUserName();
        if (userName != null && !userName.trim().isEmpty()) {
            return userName;
        }

        String email = getEmailFromProjection(projection);
        if (email != null && email.contains("@")) {
            return email.substring(0, email.indexOf('@'));
        }

        Boolean isExternal = projection.getIsExternal();
        return isExternal != null && isExternal ? "External User" : "Internal User";
    }

    private String getEmailFromProjection(ParticipantDetailsProjection projection) {
        // Priority: participant.email > user.email
        String participantEmail = projection.getEmail();
        if (participantEmail != null && !participantEmail.trim().isEmpty()) {
            return participantEmail;
        }

        String userEmail = projection.getUserEmail();
        if (userEmail != null && !userEmail.trim().isEmpty()) {
            return userEmail;
        }

        return null;
    }

    /**
     * Projection class for database queries that fetch participant details with user information.
     * Used with jOOQ's fetchInto() method for type-safe database projections.
     */
    public static class ParticipantDetailsProjection {
        private Long id;
        private Long userId;
        private String email;
        private String name;
        private Boolean isExternal;
        private String userName;
        private String userEmail;

        public ParticipantDetailsProjection() {}

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public Boolean getIsExternal() { return isExternal; }
        public void setIsExternal(Boolean isExternal) { this.isExternal = isExternal; }

        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }

        public String getUserEmail() { return userEmail; }
        public void setUserEmail(String userEmail) { this.userEmail = userEmail; }
    }

    /**
     * Simple data class for participant details used in bulk operations.
     */
    public static class ParticipantDetails {
        private final Long id;
        private final String name;
        private final String email;

        public ParticipantDetails(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }

        public Long getId() { return id; }
        public String getName() { return name; }
        public String getEmail() { return email; }
    }

    /**
     * Checks if an email is already a participant in a hub.
     *
     * @param hubId the hub ID
     * @param email the email to check
     * @return true if the email is already a participant, false otherwise
     */
    public boolean existsByHubIdAndEmail(Long hubId, String email) {
        return dsl.selectCount()
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.EMAIL.equalIgnoreCase(email))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOne(0, Integer.class) > 0;
    }

    /**
     * Finds a participant by hub ID and user ID.
     *
     * @param hubId the hub ID
     * @param userId the user ID
     * @return the participant if found, null otherwise
     */
    public HubParticipant findByHubIdAndUserId(Long hubId, Long userId) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOneInto(HubParticipant.class);
    }

    /**
     * Finds a participant by hub ID and email.
     *
     * @param hubId the hub ID
     * @param email the email
     * @return the participant if found, null otherwise
     */
    public HubParticipant findByHubIdAndEmail(Long hubId, String email) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.EMAIL.equalIgnoreCase(email))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOneInto(HubParticipant.class);
    }

    /**
     * Finds a participant by hub ID and email, including soft-deleted ones.
     * Used for re-invitation logic to reuse existing participant records.
     *
     * @param hubId the hub ID
     * @param email the email
     * @return the participant if found (including removed), null otherwise
     */
    public HubParticipant findByHubIdAndEmailIncludingRemoved(Long hubId, String email) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.EMAIL.equalIgnoreCase(email))
                .fetchOneInto(HubParticipant.class);
    }

    /**
     * Finds a participant by hub ID and user ID, including soft-deleted ones.
     * Used for re-invitation logic to reuse existing participant records.
     *
     * @param hubId the hub ID
     * @param userId the user ID
     * @return the participant if found (including removed), null otherwise
     */
    public HubParticipant findByHubIdAndUserIdIncludingRemoved(Long hubId, Long userId) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .fetchOneInto(HubParticipant.class);
    }

    /**
     * Finds a participant by user ID and hub ID.
     * This method name matches what the chat controllers expect.
     *
     * @param userId the user ID
     * @param hubId the hub ID
     * @return optional containing the participant if found
     */
    public Optional<HubParticipant> findByUserIdAndHubId(Long userId, Long hubId) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOptionalInto(HubParticipant.class);
    }

    /**
     * Finds a participant by email and hub ID.
     * This method name matches what the chat controllers expect.
     *
     * @param email the email
     * @param hubId the hub ID
     * @return optional containing the participant if found
     */
    public Optional<HubParticipant> findByEmailAndHubId(String email, Long hubId) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.EMAIL.equalIgnoreCase(email))
                .and(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOptionalInto(HubParticipant.class);
    }

    /**
     * Finds a participant by email across all hubs.
     * Used for external participant lookups.
     *
     * @param email the email
     * @return optional containing the participant if found
     */
    public Optional<HubParticipant> findByEmail(String email) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.EMAIL.equalIgnoreCase(email))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOptionalInto(HubParticipant.class);
    }

    /**
     * Bulk loads participants by IDs to avoid N+1 queries.
     * Used for optimizing chat message loading.
     *
     * @param participantIds the list of participant IDs
     * @return list of participants
     */
    public List<HubParticipant> findByIds(List<Long> participantIds) {
        if (participantIds == null || participantIds.isEmpty()) {
            return List.of();
        }

        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.ID.in(participantIds))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchInto(HubParticipant.class);
    }

    /**
     * Gets participant counts by hub IDs for bulk operations.
     * Used for optimizing hub list views.
     *
     * @param hubIds the list of hub IDs
     * @return map of hub ID to participant count
     */
    public Map<Long, Integer> getParticipantCountsByHubIds(List<Long> hubIds) {
        if (hubIds == null || hubIds.isEmpty()) {
            return Map.of();
        }

        return dsl.select(HUB_PARTICIPANT.HUB_ID, DSL.count())
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.in(hubIds))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .groupBy(HUB_PARTICIPANT.HUB_ID)
                .fetchMap(HUB_PARTICIPANT.HUB_ID, DSL.count());
    }

    /**
     * Finds participants with role-based visibility filtering.
     * Content creators can only see admins and reviewers, not other content creators.
     *
     * @param hubId the hub ID
     * @param currentUserRole the role of the current user requesting the list
     * @param role optional role filter
     * @param offset pagination offset
     * @param limit pagination limit
     * @return list of participants matching criteria and visibility rules
     */
    public List<HubParticipant> findParticipantsWithRoleBasedVisibility(Long hubId,
                                                                       org.jooq.generated.enums.HubParticipantRole currentUserRole,
                                                                       org.jooq.generated.enums.HubParticipantRole role,
                                                                       int offset, int limit) {
        var query = dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull()); // Only show non-removed participants

        // Apply role-based visibility filter
        if (currentUserRole == org.jooq.generated.enums.HubParticipantRole.content_creator) {
            // Content creators can only see admins and reviewers, not other content creators
            query = query.and(HUB_PARTICIPANT.ROLE.in(
                org.jooq.generated.enums.HubParticipantRole.admin,
                org.jooq.generated.enums.HubParticipantRole.reviewer,
                org.jooq.generated.enums.HubParticipantRole.reviewer_creator
            ));
        }
        // Admin, reviewer, and reviewer_creator can see all participants (no additional filtering)

        // Apply role filter
        if (role != null) {
            query = query.and(HUB_PARTICIPANT.ROLE.eq(role));
        }

        return query.orderBy(HUB_PARTICIPANT.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(HubParticipant.class);
    }

    /**
     * Counts participants with role-based visibility filtering.
     * Content creators can only see admins and reviewers, not other content creators.
     *
     * @param hubId the hub ID
     * @param currentUserRole the role of the current user requesting the count
     * @param role optional role filter
     * @return count of participants matching criteria and visibility rules
     */
    public long countParticipantsWithRoleBasedVisibility(Long hubId,
                                                        org.jooq.generated.enums.HubParticipantRole currentUserRole,
                                                        org.jooq.generated.enums.HubParticipantRole role) {
        var query = dsl.selectCount()
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull()); // Only show non-removed participants

        // Apply role-based visibility filter
        if (currentUserRole == org.jooq.generated.enums.HubParticipantRole.content_creator) {
            // Content creators can only see admins and reviewers, not other content creators
            query = query.and(HUB_PARTICIPANT.ROLE.in(
                org.jooq.generated.enums.HubParticipantRole.admin,
                org.jooq.generated.enums.HubParticipantRole.reviewer,
                org.jooq.generated.enums.HubParticipantRole.reviewer_creator
            ));
        }
        // Admin, reviewer, and reviewer_creator can see all participants (no additional filtering)

        if (role != null) {
            query = query.and(HUB_PARTICIPANT.ROLE.eq(role));
        }

        return query.fetchOne(0, Long.class);
    }

    /**
     * Finds a participant by ID and hub ID with access validation.
     *
     * @param participantId the participant ID
     * @param hubId the hub ID
     * @return optional containing the participant if found
     */
    public Optional<HubParticipant> findByIdAndHubId(Long participantId, Long hubId) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.ID.eq(participantId))
                .and(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOptionalInto(HubParticipant.class);
    }

    /**
     * Soft deletes a participant by setting removed_at timestamp.
     *
     * @param participantId the participant ID
     * @param hubId the hub ID for validation
     * @return true if participant was removed, false if not found
     */
    public boolean softDeleteParticipant(Long participantId, Long hubId) {
        int updated = dsl.update(HUB_PARTICIPANT)
                .set(HUB_PARTICIPANT.REMOVED_AT, LocalDateTime.now())
                .where(HUB_PARTICIPANT.ID.eq(participantId))
                .and(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .execute();

        return updated > 0;
    }

    /**
     * Updates a participant's role.
     *
     * @param participantId the participant ID
     * @param hubId the hub ID for validation
     * @param newRole the new role
     * @return true if role was updated, false if participant not found
     */
    public boolean updateParticipantRole(Long participantId, Long hubId,
                                       org.jooq.generated.enums.HubParticipantRole newRole) {
        int updated = dsl.update(HUB_PARTICIPANT)
                .set(HUB_PARTICIPANT.ROLE, newRole)
                .where(HUB_PARTICIPANT.ID.eq(participantId))
                .and(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .execute();

        return updated > 0;
    }

    /**
     * Finds a participant by magic link token.
     *
     * @param token the magic link token
     * @return optional containing the participant if found and token is valid
     */
    public Optional<HubParticipant> findByMagicLinkToken(String token) {
        return dsl.selectFrom(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.MAGIC_LINK_TOKEN.eq(token))
                .and(HUB_PARTICIPANT.MAGIC_LINK_EXPIRY.greaterThan(LocalDateTime.now()))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOptionalInto(HubParticipant.class);
    }

    /**
     * Updates participant's joined_at timestamp when they first access via magic link.
     *
     * @param participantId the participant ID
     * @return true if updated successfully
     */
    public boolean markParticipantAsJoined(Long participantId) {
        int updated = dsl.update(HUB_PARTICIPANT)
                .set(HUB_PARTICIPANT.JOINED_AT, LocalDateTime.now())
                .where(HUB_PARTICIPANT.ID.eq(participantId))
                .and(HUB_PARTICIPANT.JOINED_AT.isNull())
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .execute();

        return updated > 0;
    }

    /**
     * Reactivates a removed participant by clearing removed_at and updating invitation fields.
     * Used when re-inviting a previously removed participant.
     *
     * @param participantId the participant ID
     * @param role the new role for the participant
     * @param magicLinkToken the new magic link token (null for internal users)
     * @param magicLinkExpiry the new magic link expiry (null for internal users)
     * @param isInternal whether this is an internal user (affects joined_at)
     * @return true if reactivated successfully
     */
    public boolean reactivateParticipant(Long participantId, org.jooq.generated.enums.HubParticipantRole role,
                                       String magicLinkToken, LocalDateTime magicLinkExpiry, boolean isInternal) {
        var updateQuery = dsl.update(HUB_PARTICIPANT)
                .set(HUB_PARTICIPANT.REMOVED_AT, (LocalDateTime) null)
                .set(HUB_PARTICIPANT.ROLE, role)
                .set(HUB_PARTICIPANT.INVITED_AT, LocalDateTime.now())
                .set(HUB_PARTICIPANT.MAGIC_LINK_TOKEN, magicLinkToken)
                .set(HUB_PARTICIPANT.MAGIC_LINK_EXPIRY, magicLinkExpiry);

        // Internal users join immediately, external users join when they click the magic link
        if (isInternal) {
            updateQuery = updateQuery.set(HUB_PARTICIPANT.JOINED_AT, LocalDateTime.now());
        } else {
            updateQuery = updateQuery.set(HUB_PARTICIPANT.JOINED_AT, (LocalDateTime) null);
        }

        int updated = updateQuery
                .where(HUB_PARTICIPANT.ID.eq(participantId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNotNull())
                .execute();

        return updated > 0;
    }

    /**
     * Revokes magic link token for a participant.
     *
     * @param participantId the participant ID
     * @return true if token was revoked
     */
    public boolean revokeMagicLinkToken(Long participantId) {
        int updated = dsl.update(HUB_PARTICIPANT)
                .set(HUB_PARTICIPANT.MAGIC_LINK_TOKEN, (String) null)
                .set(HUB_PARTICIPANT.MAGIC_LINK_EXPIRY, (LocalDateTime) null)
                .where(HUB_PARTICIPANT.ID.eq(participantId))
                .execute();

        return updated > 0;
    }

    /**
     * Checks if a magic link token already exists.
     *
     * @param token the magic link token to check
     * @return true if token exists, false otherwise
     */
    public boolean existsByMagicLinkToken(String token) {
        return dsl.selectCount()
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.MAGIC_LINK_TOKEN.eq(token))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOne(0, Integer.class) > 0;
    }
}
