package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.bankdetails.repository.BankDetailsRepositoryImpl;
import com.collabhub.be.modules.brands.repository.BrandRepositoryImpl;
import com.collabhub.be.modules.companies.repository.AccountCompanyRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.InvoiceSnapshotResponse;
import com.collabhub.be.modules.invoice.model.InvoiceSnapshot;
import com.collabhub.be.modules.invoice.model.InvoiceSnapshotEntity;
import com.collabhub.be.modules.invoice.repository.InvoiceSnapshotRepository;
import com.collabhub.be.util.JsonbUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.AccountCompany;
import org.jooq.generated.tables.pojos.BankDetails;
import org.jooq.generated.tables.pojos.Brand;
import org.jooq.generated.tables.pojos.Invoice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;
import java.util.Optional;

/**
 * Service for creating and managing invoice snapshots with hash-based deduplication.
 * Snapshots are only created when invoice status changes from DRAFT to prevent data inconsistency.
 * Uses SHA-256 hashing for deduplication to reduce storage space.
 */
@Service
public class InvoiceSnapshotService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceSnapshotService.class);

    private final AccountCompanyRepositoryImpl accountCompanyRepository;
    private final BrandRepositoryImpl brandRepository;
    private final BankDetailsRepositoryImpl bankDetailsRepository;
    private final InvoiceSnapshotRepository snapshotRepository;
    private final ObjectMapper objectMapper;

    public InvoiceSnapshotService(AccountCompanyRepositoryImpl accountCompanyRepository,
                                  BrandRepositoryImpl brandRepository,
                                  BankDetailsRepositoryImpl bankDetailsRepository,
                                  InvoiceSnapshotRepository snapshotRepository,
                                  ObjectMapper objectMapper) {
        this.accountCompanyRepository = accountCompanyRepository;
        this.brandRepository = brandRepository;
        this.bankDetailsRepository = bankDetailsRepository;
        this.snapshotRepository = snapshotRepository;
        this.objectMapper = objectMapper;
    }

    /**
     * Creates snapshots for issuer, recipient, and bank details using hash-based deduplication.
     * This method should only be called when invoice status changes from DRAFT.
     *
     * @param invoice the invoice to update with snapshot hashes
     * @param issuerId the issuer company ID
     * @param recipientId the recipient company ID
     * @param bankDetailsId the bank details ID
     */
    @Transactional
    public void createSnapshots(Invoice invoice, Long issuerId, Long recipientId, Long bankDetailsId) {
        logger.debug("Creating snapshots for invoice: issuer={}, recipient={}, bankDetails={}",
                    issuerId, recipientId, bankDetailsId);

        // Create issuer snapshot
        AccountCompany issuer = accountCompanyRepository.findByIdAndAccountId(issuerId, invoice.getAccountId())
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                        "Issuer company not found: " + issuerId));

        String issuerHash = createAndStoreSnapshot(issuer, InvoiceSnapshotEntity.EntityType.ISSUER);
        invoice.setIssuerSnapshotHash(issuerHash);

        // Create recipient snapshot
        Brand recipient = brandRepository.findByIdAndAccountId(recipientId, invoice.getAccountId())
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                        "Recipient company not found: " + recipientId));

        String recipientHash = createAndStoreSnapshot(recipient, InvoiceSnapshotEntity.EntityType.RECIPIENT);
        invoice.setRecipientSnapshotHash(recipientHash);

        // Create bank details snapshot
        BankDetails bankDetails = bankDetailsRepository.findByIdAndAccountId(bankDetailsId, invoice.getAccountId())
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                        "Bank details not found: " + bankDetailsId));

        String bankDetailsHash = createAndStoreSnapshot(bankDetails, InvoiceSnapshotEntity.EntityType.BANK_DETAILS);
        invoice.setBankDetailsSnapshotHash(bankDetailsHash);

        logger.debug("Successfully created snapshots for invoice");
    }

    /**
     * Creates and stores a snapshot with hash-based deduplication.
     *
     * @param entity the entity to snapshot
     * @param entityType the type of entity
     * @return the hash of the stored snapshot
     */
    private String createAndStoreSnapshot(Object entity, InvoiceSnapshotEntity.EntityType entityType) {
        // Create snapshot object based on entity type
        Object snapshot = switch (entityType) {
            case ISSUER -> createIssuerSnapshot((AccountCompany) entity);
            case RECIPIENT -> createRecipientSnapshot((Brand) entity);
            case BANK_DETAILS -> createBankDetailsSnapshot((BankDetails) entity);
        };

        // Convert to JSONB
        JSONB snapshotData = convertToJsonb(snapshot);

        // Calculate hash (excluding timestamps for better deduplication)
        String hash = calculateSnapshotHash(snapshotData);

        // Store or get existing snapshot
        InvoiceSnapshotEntity snapshotEntity = new InvoiceSnapshotEntity(hash, entityType, snapshotData);
        snapshotRepository.saveOrGetExisting(snapshotEntity);

        return hash;
    }

    /**
     * Creates an issuer snapshot from account company data.
     *
     * @param company the account company
     * @return the issuer snapshot
     */
    private InvoiceSnapshot.IssuerSnapshot createIssuerSnapshot(AccountCompany company) {
        return new InvoiceSnapshot.IssuerSnapshot(
                company.getId(),
                company.getCompanyName(),
                company.getAddressStreet(),
                company.getAddressCity(),
                company.getAddressPostalCode(),
                company.getAddressCountry(),
                company.getVatNumber(),
                company.getRegistrationNumber(),
                company.getPhone(),
                company.getEmail(),
                company.getWebsite()
        );
    }

    /**
     * Creates a recipient snapshot from brand data.
     *
     * @param brand the brand
     * @return the recipient snapshot
     */
    private InvoiceSnapshot.RecipientSnapshot createRecipientSnapshot(Brand brand) {
        return new InvoiceSnapshot.RecipientSnapshot(
                brand.getId(),
                brand.getCompanyName(),
                brand.getAddressStreet(),
                brand.getAddressCity(),
                brand.getAddressPostalCode(),
                brand.getAddressCountry(),
                brand.getVatNumber(),
                brand.getRegistrationNumber(),
                brand.getEmail(),
                brand.getPhone()
        );
    }

    /**
     * Creates a bank details snapshot from bank details data.
     *
     * @param bankDetails the bank details
     * @return the bank details snapshot
     */
    private InvoiceSnapshot.BankDetailsSnapshot createBankDetailsSnapshot(BankDetails bankDetails) {
        return new InvoiceSnapshot.BankDetailsSnapshot(
                bankDetails.getId(),
                bankDetails.getName(),
                bankDetails.getBankName(),
                bankDetails.getIban(),
                bankDetails.getBicswift()
        );
    }

    /**
     * Calculates SHA-256 hash of snapshot data for deduplication.
     *
     * @param snapshotData the JSONB snapshot data
     * @return the SHA-256 hash as hex string
     */
    private String calculateSnapshotHash(JSONB snapshotData) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(snapshotData.data().getBytes(StandardCharsets.UTF_8));

            // Convert to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }

    /**
     * Retrieves snapshot data by hash.
     *
     * @param hash the snapshot hash
     * @return optional containing the snapshot data if found
     */
    public Optional<JSONB> getSnapshotByHash(String hash) {
        return snapshotRepository.findByHash(hash)
                .map(InvoiceSnapshotEntity::getSnapshotData);
    }

    /**
     * Converts a snapshot object to JSONB format for database storage.
     *
     * @param snapshot the snapshot object
     * @return the JSONB representation
     */
    private JSONB convertToJsonb(Object snapshot) {
        JSONB result = JsonbUtil.toJsonb(snapshot, objectMapper);
        if (result == null) {
            throw new RuntimeException("Failed to create invoice snapshot");
        }
        return result;
    }

    /**
     * Retrieves issuer snapshot response by hash.
     *
     * @param hash the snapshot hash
     * @return the issuer snapshot response, or empty response if not found
     */
    public InvoiceSnapshotResponse.IssuerSnapshotResponse getIssuerSnapshotByHash(String hash) {
        if (hash == null) {
            return new InvoiceSnapshotResponse.IssuerSnapshotResponse();
        }

        return getSnapshotByHash(hash)
                .map(jsonb -> JsonbUtil.parseJsonb(jsonb, InvoiceSnapshot.IssuerSnapshot.class, objectMapper))
                .map(this::convertToIssuerResponse)
                .orElse(new InvoiceSnapshotResponse.IssuerSnapshotResponse());
    }

    /**
     * Retrieves recipient snapshot response by hash.
     *
     * @param hash the snapshot hash
     * @return the recipient snapshot response, or empty response if not found
     */
    public InvoiceSnapshotResponse.RecipientSnapshotResponse getRecipientSnapshotByHash(String hash) {
        if (hash == null) {
            return new InvoiceSnapshotResponse.RecipientSnapshotResponse();
        }

        return getSnapshotByHash(hash)
                .map(jsonb -> JsonbUtil.parseJsonb(jsonb, InvoiceSnapshot.RecipientSnapshot.class, objectMapper))
                .map(this::convertToRecipientResponse)
                .orElse(new InvoiceSnapshotResponse.RecipientSnapshotResponse());
    }

    /**
     * Retrieves bank details snapshot response by hash.
     *
     * @param hash the snapshot hash
     * @return the bank details snapshot response, or empty response if not found
     */
    public InvoiceSnapshotResponse.BankDetailsSnapshotResponse getBankDetailsSnapshotByHash(String hash) {
        if (hash == null) {
            return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse();
        }

        return getSnapshotByHash(hash)
                .map(jsonb -> JsonbUtil.parseJsonb(jsonb, InvoiceSnapshot.BankDetailsSnapshot.class, objectMapper))
                .map(this::convertToBankDetailsResponse)
                .orElse(new InvoiceSnapshotResponse.BankDetailsSnapshotResponse());
    }

    /**
     * Converts issuer snapshot to response DTO.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse convertToIssuerResponse(InvoiceSnapshot.IssuerSnapshot snapshot) {
        return new InvoiceSnapshotResponse.IssuerSnapshotResponse(
                snapshot.getId(),
                snapshot.getName(),
                snapshot.getAddress(),
                snapshot.getCity(),
                snapshot.getPostalCode(),
                snapshot.getCountry(),
                snapshot.getVatNumber(),
                snapshot.getRegistrationNumber(),
                snapshot.getPhone(),
                snapshot.getEmail(),
                snapshot.getWebsite()
        );
    }

    /**
     * Converts recipient snapshot to response DTO.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse convertToRecipientResponse(InvoiceSnapshot.RecipientSnapshot snapshot) {
        return new InvoiceSnapshotResponse.RecipientSnapshotResponse(
                snapshot.getId(),
                snapshot.getName(),
                snapshot.getAddress(),
                snapshot.getCity(),
                snapshot.getPostalCode(),
                snapshot.getCountry(),
                snapshot.getVatNumber(),
                snapshot.getRegistrationNumber(),
                snapshot.getEmail(),
                snapshot.getPhone()
        );
    }

    /**
     * Converts bank details snapshot to response DTO.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse convertToBankDetailsResponse(InvoiceSnapshot.BankDetailsSnapshot snapshot) {
        return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse(
                snapshot.getId(),
                snapshot.getAccountName(),
                snapshot.getBankName(),
                snapshot.getIban(),
                snapshot.getBic()
        );
    }
}
