package com.collabhub.be.modules.posts.dto;

import com.collabhub.be.modules.posts.constants.PostConstants;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.jooq.generated.enums.ReviewStatus;

/**
 * Request DTO for creating or updating a post review.
 * Supports upsert behavior - creates new review or updates existing one.
 */
public class PostReviewCreateRequest {

    @NotNull(message = PostConstants.REVIEW_DECISION_REQUIRED_MESSAGE)
    private ReviewStatus decision;

    @NotBlank(message = PostConstants.REVIEW_COMMENT_REQUIRED_MESSAGE)
    @Size(max = PostConstants.MAX_REVIEW_COMMENT_LENGTH, message = PostConstants.REVIEW_COMMENT_TOO_LONG_MESSAGE)
    private String comment;

    public PostReviewCreateRequest() {}

    public PostReviewCreateRequest(ReviewStatus decision, String comment) {
        this.decision = decision;
        this.comment = comment;
    }

    public ReviewStatus getDecision() {
        return decision;
    }

    public void setDecision(ReviewStatus decision) {
        this.decision = decision;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Override
    public String toString() {
        return "PostReviewCreateRequest{" +
                "decision=" + decision +
                ", comment='" + comment + '\'' +
                '}';
    }
}
