package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.collaborationhub.converter.CollaborationHubConverter;
import com.collabhub.be.modules.collaborationhub.converter.HubParticipantConverter;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.chat.service.ChatChannelService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service for managing collaboration hubs and their participants.
 * Handles business logic, validation, and multi-tenancy.
 */
@Service
@Transactional
public class CollaborationHubService {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationHubService.class);
    private static final String HUB_NOT_FOUND_MESSAGE = "Hub not found with ID: ";
    private static final String INSUFFICIENT_PERMISSIONS_MESSAGE = "Insufficient permissions to update hub";
    private static final String HUB_NAME_EXISTS_MESSAGE = "Hub name already exists: ";

    private final CollaborationHubRepositoryImpl hubRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationHubConverter hubConverter;
    private final HubParticipantConverter participantConverter;
    private final ChatChannelService chatChannelService;
    private final UserRepository userRepository;

    public CollaborationHubService(CollaborationHubRepositoryImpl hubRepository,
                                 HubParticipantRepositoryImpl participantRepository,
                                 CollaborationHubConverter hubConverter,
                                 HubParticipantConverter participantConverter,
                                 ChatChannelService chatChannelService,
                                 UserRepository userRepository) {
        this.hubRepository = hubRepository;
        this.participantRepository = participantRepository;
        this.hubConverter = hubConverter;
        this.participantConverter = participantConverter;
        this.chatChannelService = chatChannelService;
        this.userRepository = userRepository;
    }

    /**
     * Creates a new collaboration hub and automatically adds the creator as an admin participant.
     *
     * @param request the hub creation request
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID of the creator
     * @param userEmail the email of the creator
     * @return the created hub response
     */
    @Transactional
    public CollaborationHubResponse createHub(CollaborationHubCreateRequest request, Long accountId,
                                            Long userId, String userEmail) {
        logger.info("Creating collaboration hub '{}' for account {} by user {}",
                   request.getName(), accountId, userId);

        validateHubNameUniqueness(request.getName(), accountId, null);

        CollaborationHub hub = createHubEntity(request, accountId, userId);
        createAdminParticipant(hub.getId(), userId, userEmail);
        createDefaultChatChannels(hub.getId());

        logger.info("Successfully created collaboration hub with ID {} for account {}",
                   hub.getId(), accountId);

        return buildHubResponse(hub, HubParticipantRole.admin);
    }

    /**
     * Updates an existing collaboration hub.
     *
     * @param hubId the hub ID
     * @param request the update request
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID making the request
     * @return the updated hub response
     */
    @Transactional
    public CollaborationHubResponse updateHub(Long hubId, CollaborationHubUpdateRequest request,
                                            Long accountId, Long userId) {
        logger.info("Updating collaboration hub {} for account {} by user {}", hubId, accountId, userId);

        CollaborationHub hub = validateHubAccessAndAdminRole(hubId, accountId, userId);
        validateHubNameForUpdate(hub, request.getName(), accountId, hubId);

        updateHubEntity(hub, request);

        logger.info("Successfully updated collaboration hub {} for account {}", hubId, accountId);

        return getHubDetails(hubId, accountId, userId);
    }

    /**
     * Gets detailed information about a collaboration hub.
     *
     * @param hubId the hub ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID making the request
     * @return the hub details
     */
    @Transactional(readOnly = true)
    public CollaborationHubResponse getHubDetails(Long hubId, Long accountId, Long userId) {
        logger.debug("Retrieving collaboration hub details for hub {} by user {}", hubId, userId);

        // Find hub and validate access
        CollaborationHub hub = hubRepository.findByIdWithAccess(hubId, accountId, userId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_NOT_FOUND, "Hub not found with ID: " + hubId));

        // Get user's role
        HubParticipantRole userRole = hubRepository.getUserRoleInHub(hubId, userId);

        // Get brand name
        String brandName = hubRepository.getBrandNameForHub(hubId);

        return hubConverter.toResponse(hub, brandName, userRole);
    }

    /**
     * Gets a paginated list of collaboration hubs for a user.
     *
     * @param pageRequest the pagination request
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID making the request
     * @param nameFilter optional name filter
     * @param brandId optional brand filter
     * @return paginated list of hubs
     */
    @Transactional(readOnly = true)
    public PageResponse<CollaborationHubListItemDto> getHubs(PageRequest pageRequest, Long accountId, 
                                                            Long userId, String nameFilter, Long brandId) {
        logger.debug("Retrieving collaboration hubs for account {} by user {} with filters: name='{}', brandId={}", 
                    accountId, userId, nameFilter, brandId);

        int offset = pageRequest.getPage() * pageRequest.getSize();
        
        // Get hubs with pagination
        List<CollaborationHub> hubs = hubRepository.findHubsWithPagination(
                accountId, userId, nameFilter, brandId, offset, pageRequest.getSize());
        
        // Get total count
        long totalCount = hubRepository.countHubsWithFilter(accountId, userId, nameFilter, brandId);

        // Convert to list items
        List<CollaborationHubListItemDto> hubListItems = convertHubsToListItems(hubs, userId);

        return new PageResponse<>(hubListItems, pageRequest, totalCount);
    }

    /**
     * Deletes a collaboration hub (admin only).
     *
     * @param hubId the hub ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID making the request
     */
    @Transactional
    public void deleteHub(Long hubId, Long accountId, Long userId) {
        logger.info("Deleting collaboration hub {} for account {} by user {}", hubId, accountId, userId);

        validateHubAccessAndAdminRole(hubId, accountId, userId);
        hubRepository.deleteById(hubId);

        logger.info("Successfully deleted collaboration hub {} for account {}", hubId, accountId);
    }

    /**
     * Converts hub participants to response DTOs.
     */
    private List<HubParticipantResponse> convertParticipantsToResponses(List<HubParticipant> participants) {
        if (participants == null || participants.isEmpty()) {
            return List.of();
        }

        List<Long> participantIds = participants.stream()
                .map(HubParticipant::getId)
                .toList();

        Map<Long, String> participantNames = participantRepository.findParticipantNamesByIds(participantIds);

        return participants.stream()
                .map(participant -> {
                    String name = participantNames.get(participant.getId());
                    if (name == null && participant.getIsExternal()) {
                        // For external users, use email prefix as name if no display name
                        String email = participant.getEmail();
                        name = email.contains("@") ? email.substring(0, email.indexOf('@')) : email;
                    }
                    return participantConverter.toResponse(participant, name);
                })
                .toList();
    }

    /**
     * Converts hubs to list item DTOs with bulk loading to avoid N+1 queries.
     */
    private List<CollaborationHubListItemDto> convertHubsToListItems(List<CollaborationHub> hubs, Long userId) {
        if (hubs == null || hubs.isEmpty()) {
            return List.of();
        }

        List<Long> hubIds = hubs.stream().map(CollaborationHub::getId).toList();

        // Bulk load brand names and user roles to avoid N+1 queries
        Map<Long, String> brandNames = hubRepository.getBrandNamesForHubs(hubIds);
        Map<Long, HubParticipantRole> userRoles = hubRepository.getUserRolesInHubs(hubIds, userId);

        return hubs.stream()
                .map(hub -> {
                    String brandName = brandNames.get(hub.getId());
                    HubParticipantRole userRole = userRoles.get(hub.getId());
                    return hubConverter.toListItem(hub, brandName, userRole);
                })
                .toList();
    }

    /**
     * Calculates hub statistics.
     */
    private HubStatsDto calculateHubStats(Long hubId) {
        // Placeholder implementation - would calculate from actual post and chat data
        return new HubStatsDto(0L, 0L, 0L, 0L);
    }

    /**
     * Gets user display name by user ID.
     */
    private String getUserDisplayName(Long userId) {
        if (userId == null) {
            return "Unknown User";
        }

        org.jooq.generated.tables.pojos.User user = userRepository.findById(userId);
        if (user != null && user.getDisplayName() != null) {
            return user.getDisplayName();
        }

        // Fallback to email if name is not available
        if (user != null && user.getEmail() != null) {
            return user.getEmail().substring(0, user.getEmail().indexOf('@'));
        }

        return "User " + userId;
    }

    /**
     * Validates hub name uniqueness within an account.
     */
    private void validateHubNameUniqueness(String hubName, Long accountId, Long excludeHubId) {
        if (hubRepository.existsByNameAndAccount(hubName, accountId, excludeHubId)) {
            throw new ConflictException(ErrorCode.HUB_NAME_ALREADY_EXISTS, HUB_NAME_EXISTS_MESSAGE + hubName);
        }
    }

    /**
     * Creates the hub entity and persists it.
     */
    private CollaborationHub createHubEntity(CollaborationHubCreateRequest request, Long accountId, Long userId) {
        CollaborationHub hub = hubConverter.toCollaborationHub(request, accountId, userId);
        hubRepository.insert(hub);
        return hub;
    }

    /**
     * Creates admin participant for the hub creator.
     */
    private void createAdminParticipant(Long hubId, Long userId, String userEmail) {
        HubParticipant adminParticipant = participantConverter.createInternalParticipant(
                hubId, userId, userEmail, HubParticipantRole.admin);
        participantRepository.insert(adminParticipant);
    }

    /**
     * Creates default chat channels for the hub.
     */
    private void createDefaultChatChannels(Long hubId) {
        chatChannelService.createDefaultChannelsForHub(hubId);
    }

    /**
     * Builds hub response with brand name lookup.
     */
    private CollaborationHubResponse buildHubResponse(CollaborationHub hub, HubParticipantRole userRole) {
        String brandName = hubRepository.getBrandNameForHub(hub.getId());
        return hubConverter.toResponse(hub, brandName, userRole);
    }

    /**
     * Validates hub access and admin role.
     */
    private CollaborationHub validateHubAccessAndAdminRole(Long hubId, Long accountId, Long userId) {
        CollaborationHub hub = hubRepository.findByIdWithAccess(hubId, accountId, userId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_NOT_FOUND, HUB_NOT_FOUND_MESSAGE + hubId));

        HubParticipantRole userRole = hubRepository.getUserRoleInHub(hubId, userId);
        if (userRole != HubParticipantRole.admin) {
            throw new ConflictException(ErrorCode.INSUFFICIENT_PERMISSIONS, INSUFFICIENT_PERMISSIONS_MESSAGE);
        }

        return hub;
    }

    /**
     * Validates hub name for update operations.
     */
    private void validateHubNameForUpdate(CollaborationHub hub, String newName, Long accountId, Long hubId) {
        if (!hub.getName().equalsIgnoreCase(newName)) {
            validateHubNameUniqueness(newName, accountId, hubId);
        }
    }

    /**
     * Updates hub entity with new data.
     */
    private void updateHubEntity(CollaborationHub hub, CollaborationHubUpdateRequest request) {
        hubConverter.updateCollaborationHub(hub, request);
        hubRepository.update(hub);
    }
}
