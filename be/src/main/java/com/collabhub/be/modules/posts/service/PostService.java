package com.collabhub.be.modules.posts.service;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.converter.PostConverter;
import com.collabhub.be.modules.posts.dto.*;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostReviewerRepositoryImpl;
import com.collabhub.be.modules.media.service.MediaService;
import com.collabhub.be.modules.media.repository.PostMediaRepositoryImpl;
import com.collabhub.be.modules.media.repository.MediaRepositoryImpl;
import com.collabhub.be.modules.media.dto.MediaDto;
import com.collabhub.be.service.S3Service;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.annotation.Counted;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Service for managing posts in collaboration hubs.
 * Handles post creation, updates, media uploads, and access control.
 */
@Service
public class PostService {

    private static final Logger logger = LoggerFactory.getLogger(PostService.class);

    private final PostRepositoryImpl postRepository;
    private final PostReviewerRepositoryImpl postReviewerRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final PostConverter postConverter;
    private final S3Service s3Service;
    private final MediaService mediaService;
    private final PostMediaRepositoryImpl postMediaRepository;
    private final MediaRepositoryImpl mediaRepository;

    public PostService(PostRepositoryImpl postRepository,
                      PostReviewerRepositoryImpl postReviewerRepository,
                      HubParticipantRepositoryImpl participantRepository,
                      PostConverter postConverter,
                      S3Service s3Service,
                      MediaService mediaService,
                      PostMediaRepositoryImpl postMediaRepository,
                      MediaRepositoryImpl mediaRepository) {
        this.postRepository = postRepository;
        this.postReviewerRepository = postReviewerRepository;
        this.participantRepository = participantRepository;
        this.postConverter = postConverter;
        this.s3Service = s3Service;
        this.mediaService = mediaService;
        this.postMediaRepository = postMediaRepository;
        this.mediaRepository = mediaRepository;
    }

    /**
     * Creates a new post in a collaboration hub.
     */
    @Transactional
    @Timed(value = "post.create", description = "Time taken to create a post")
    @Counted(value = "post.create.attempts", description = "Number of post creation attempts")
    public PostResponse createPost(Long hubId, PostCreateRequest request, Long accountId, Long userId) {
        logger.debug("Creating post in hub {} by user {}", hubId, userId);

        HubParticipant participant = validatePostCreation(hubId, userId, accountId);
        Post post = createPostEntity(request, hubId, participant.getId());
        processPostCreationExtras(post.getId(), hubId, request, accountId);
        logPostCreation(post.getId(), hubId, userId, accountId, request);

        return getPostDetails(post.getId(), accountId, userId);
    }

    /**
     * Creates the post entity and persists it to the database.
     */
    private Post createPostEntity(PostCreateRequest request, Long hubId, Long creatorParticipantId) {
        Post post = postConverter.toPost(request, hubId, creatorParticipantId);
        postRepository.insert(post);
        return post;
    }

    /**
     * Processes media associations and reviewer assignments for a new post.
     */
    private void processPostCreationExtras(Long postId, Long hubId, PostCreateRequest request, Long accountId) {
        processMediaAssociations(postId, request.getMediaUris(), accountId);
        assignReviewersToPost(postId, hubId, request.getReviewerIds(), accountId);
    }

    /**
     * Logs post creation with relevant metrics.
     */
    private void logPostCreation(Long postId, Long hubId, Long userId, Long accountId, PostCreateRequest request) {
        logger.info("POST_CREATED: postId={}, hubId={}, userId={}, accountId={}, hasMedia={}, hasReviewers={}",
                   postId, hubId, userId, accountId,
                   (request.getMediaUris() != null && !request.getMediaUris().isEmpty()),
                   (request.getReviewerIds() != null && !request.getReviewerIds().isEmpty()));
    }

    /**
     * Gets a paginated list of posts with role-based filtering.
     */
    @Transactional(readOnly = true)
    @Timed(value = "post.list", description = "Time taken to list posts")
    public PostListResponse getPosts(Long hubId, PageRequest pageRequest, String filter,
                                   ReviewStatus status, Long accountId, Long userId) {
        logger.debug("Retrieving posts for hub {} with filter: {}, status: {}", hubId, filter, status);

        // Validate hub access and get user's role
        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, userId, accountId);

        int offset = pageRequest.getPage() * pageRequest.getSize();

        // Get posts with role-based access
        List<Post> posts = postRepository.findPostsWithRoleBasedAccess(
                hubId, userId, participant.getRole(), filter, status, offset, pageRequest.getSize());

        // Get total count
        long totalElements = postRepository.countPostsWithRoleBasedAccess(
                hubId, userId, participant.getRole(), filter, status);

        // Convert to response DTOs
        List<PostListItemResponse> postItems = convertPostsToListItems(posts, userId, accountId);

        // Create filter information
        PostListResponse.PostFilters filters = createFilterInfo(filter, status, participant.getRole());

        return new PostListResponse(postItems, pageRequest, totalElements, filters);
    }

    /**
     * Gets detailed information about a specific post.
     */
    @Transactional(readOnly = true)
    public PostResponse getPostDetails(Long postId, Long accountId, Long userId) {
        logger.debug("Retrieving post details for post {} by user {}", postId, userId);

        Post post = findAndValidatePostAccess(postId, userId, accountId);
        PostDetailData detailData = gatherPostDetailData(post, userId, accountId);

        return postConverter.toResponseWithPresignedUrls(
            post, detailData.mediaList(), detailData.creator(),
            detailData.reviewers(), detailData.permissions()
        );
    }

    /**
     * Finds a post and validates user access to it.
     */
    private Post findAndValidatePostAccess(Long postId, Long userId, Long accountId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE + ": " + postId);
        }

        if (!postRepository.canUserAccessPost(postId, userId, accountId)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }

        return post;
    }

    /**
     * Gathers all data needed for post details response.
     */
    private PostDetailData gatherPostDetailData(Post post, Long userId, Long accountId) {
        List<Media> mediaList = mediaService.findMediaByPostId(post.getId());
        PostResponse.PostCreator creator = getPostCreator(post.getCreatorParticipantId());
        List<PostResponse.PostReviewer> reviewers = getPostReviewers(post.getId());
        PostResponse.PostPermissions permissions = calculatePostPermissions(post, userId, accountId);

        return new PostDetailData(mediaList, creator, reviewers, permissions);
    }

    /**
     * Record to hold post detail data.
     */
    private record PostDetailData(
        List<Media> mediaList,
        PostResponse.PostCreator creator,
        List<PostResponse.PostReviewer> reviewers,
        PostResponse.PostPermissions permissions
    ) {}

    /**
     * Updates an existing post.
     */
    @Transactional
    public PostResponse updatePost(Long postId, PostUpdateRequest request, Long accountId, Long userId) {
        logger.debug("Updating post {} by user {}", postId, userId);

        Post post = findAndValidatePostForEdit(postId, userId);
        updatePostMedia(postId, request, accountId);
        updatePostEntity(post, request);
        logPostUpdate(postId, userId, accountId, request);

        return getPostDetails(postId, accountId, userId);
    }

    /**
     * Finds a post and validates user can edit it.
     */
    private Post findAndValidatePostForEdit(Long postId, Long userId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE + ": " + postId);
        }

        if (!canUserEditPost(post, userId)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, PostConstants.CANNOT_EDIT_POST_MESSAGE);
        }

        return post;
    }

    /**
     * Updates post media associations if provided in the request.
     */
    private void updatePostMedia(Long postId, PostUpdateRequest request, Long accountId) {
        if (request.getMediaUris() != null) {
            List<Long> mediaIds = request.getMediaUris().isEmpty() ?
                List.of() : createOrValidateMediaRecords(request.getMediaUris(), accountId);
            postMediaRepository.updatePostMediaAssociations(postId, mediaIds);
        }
    }

    /**
     * Updates the post entity with new data.
     */
    private void updatePostEntity(Post post, PostUpdateRequest request) {
        postConverter.updatePost(post, request);
        postRepository.update(post);
    }

    /**
     * Logs post update with relevant metrics.
     */
    private void logPostUpdate(Long postId, Long userId, Long accountId, PostUpdateRequest request) {
        logger.info("POST_UPDATED: postId={}, userId={}, accountId={}, hasMediaUpdate={}",
                   postId, userId, accountId,
                   (request.getMediaUris() != null && !request.getMediaUris().isEmpty()));
    }

    /**
     * Deletes a post.
     */
    @Transactional
    @Timed(value = "post.delete", description = "Time taken to delete a post")
    @Counted(value = "post.delete.attempts", description = "Number of post deletion attempts")
    public void deletePost(Long postId, Long accountId, Long userId) {
        logger.debug("Deleting post {} by user {}", postId, userId);

        Post post = findAndValidatePostForDeletion(postId, userId);
        performPostDeletion(post, accountId);
        logPostDeletion(postId, userId, accountId, post.getHubId());
    }

    /**
     * Finds a post and validates user can delete it.
     */
    private Post findAndValidatePostForDeletion(Long postId, Long userId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE + ": " + postId);
        }

        if (!canUserDeletePost(post, userId)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, PostConstants.CANNOT_DELETE_POST_MESSAGE);
        }

        return post;
    }

    /**
     * Performs the actual post deletion including media cleanup.
     */
    private void performPostDeletion(Post post, Long accountId) {
        deleteAssociatedMediaFiles(post, accountId);

        boolean deleted = postRepository.softDeletePost(post.getId());
        if (!deleted) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE + ": " + post.getId());
        }
    }

    /**
     * Logs post deletion with relevant metrics.
     */
    private void logPostDeletion(Long postId, Long userId, Long accountId, Long hubId) {
        logger.info("POST_DELETED: postId={}, userId={}, accountId={}, hubId={}",
                   postId, userId, accountId, hubId);
    }

    /**
     * Uploads a media file for posts using the new media architecture.
     */
    @Transactional
    public FileUploadResponse uploadMedia(MultipartFile file, Long accountId) {
        logger.debug("Uploading media file: {} for account {}", file.getOriginalFilename(), accountId);

        // Use MediaService to upload and create media record
        MediaDto mediaDto = mediaService.uploadMedia(file, accountId, S3Properties.ResourceType.POSTS);
        FileType type = FileType.fromMimeType(file.getContentType());

        FileUploadResponse response = new FileUploadResponse(
                mediaDto.getUrl(),
                file.getOriginalFilename(),
                file.getSize(),
                file.getContentType(),
                type
        );

        logger.info("Successfully uploaded media file: {} with ID: {} for account {}",
                   mediaDto.getUrl(), mediaDto.getId(), accountId);
        return response;
    }

    // Private helper methods

    /**
     * Validates post creation requirements and returns participant info.
     */
    private HubParticipant validatePostCreation(Long hubId, Long userId, Long accountId) {
        HubParticipant participant = validateHubAccessAndGetParticipant(hubId, userId, accountId);
        validateCanCreatePost(participant.getRole());
        return participant;
    }

    /**
     * Processes media associations for a post.
     */
    private void processMediaAssociations(Long postId, List<String> mediaUris, Long accountId) {
        if (mediaUris != null && !mediaUris.isEmpty()) {
            List<Long> mediaIds = createOrValidateMediaRecords(mediaUris, accountId);
            postMediaRepository.createPostMediaAssociations(postId, mediaIds);
        }
    }

    /**
     * Assigns reviewers to a post and sends notifications.
     */
    private void assignReviewersToPost(Long postId, Long hubId, List<Long> reviewerIds, Long accountId) {
        if (reviewerIds != null && !reviewerIds.isEmpty()) {
            createPostReviewers(postId, hubId, reviewerIds, accountId);
            sendReviewerNotifications(postId, hubId, reviewerIds, accountId);
        }
    }

    private HubParticipant validateHubAccessAndGetParticipant(Long hubId, Long userId, Long accountId) {
        HubParticipant participant = participantRepository.findByHubIdAndUserId(hubId, userId);
        if (participant == null || participant.getRemovedAt() != null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.USER_NOT_PARTICIPANT_MESSAGE + ": " + hubId);
        }
        return participant;
    }

    private void validateCanCreatePost(HubParticipantRole role) {
        if (role != HubParticipantRole.admin &&
            role != HubParticipantRole.content_creator &&
            role != HubParticipantRole.reviewer_creator) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.CANNOT_CREATE_POSTS_MESSAGE + ": " + role);
        }
    }



    private boolean canUserEditPost(Post post, Long userId) {
        // Post creator can edit
        if (postRepository.isPostCreator(post.getId(), userId)) {
            return true;
        }

        // Hub admin can edit
        HubParticipantRole userRole = postRepository.getUserRoleInHub(post.getHubId(), userId);
        return userRole == HubParticipantRole.admin;
    }

    private boolean canUserDeletePost(Post post, Long userId) {
        // Only post creator and hub admin can delete
        return canUserEditPost(post, userId);
    }

    private PostResponse.PostCreator getPostCreator(Long creatorParticipantId) {
        try {
            // Use bulk fetch method for consistency (even for single participant)
            Map<Long, HubParticipantRepositoryImpl.ParticipantDetails> creatorDetailsMap =
                    participantRepository.findParticipantDetailsByIds(List.of(creatorParticipantId));

            HubParticipantRepositoryImpl.ParticipantDetails creatorDetails =
                    creatorDetailsMap.get(creatorParticipantId);

            if (creatorDetails != null) {
                return postConverter.createPostCreator(
                        creatorDetails.getId(),
                        creatorDetails.getName(),
                        creatorDetails.getEmail());
            } else {
                return createCreatorFallback(creatorParticipantId);
            }
        } catch (Exception e) {
            logger.error("Unexpected error getting post creator {}: {}", creatorParticipantId, e.getMessage());
            return createCreatorFallback(creatorParticipantId);
        }
    }

    /**
     * Creates a fallback PostCreator for missing participant details.
     */
    private PostResponse.PostCreator createCreatorFallback(Long creatorParticipantId) {
        logger.warn("Creator participant not found: {}", creatorParticipantId);
        return postConverter.createPostCreator(
                creatorParticipantId, "Unknown Creator", "<EMAIL>");
    }

    /**
     * Creates post reviewers for assigned reviewer IDs.
     */
    private void createPostReviewers(Long postId, Long hubId, List<Long> reviewerIds, Long accountId) {
        for (Long reviewerId : reviewerIds) {
            // Validate reviewer is a participant in the hub
            HubParticipant reviewer = participantRepository.findById(reviewerId);
            if (reviewer == null || !reviewer.getHubId().equals(hubId) || reviewer.getRemovedAt() != null) {
                logger.warn("Invalid reviewer ID {} for post {} in hub {}", reviewerId, postId, hubId);
                continue;
            }

            // Validate reviewer has review permissions
            if (reviewer.getRole() != HubParticipantRole.admin &&
                reviewer.getRole() != HubParticipantRole.reviewer &&
                reviewer.getRole() != HubParticipantRole.reviewer_creator) {
                logger.warn("Reviewer {} does not have review permissions for post {}", reviewerId, postId);
                continue;
            }

            // Create post reviewer record
            postReviewerRepository.createPostReviewer(postId, reviewerId);
            logger.info("Created reviewer assignment: post={}, reviewer={}", postId, reviewerId);
        }
    }

    /**
     * Sends notifications to assigned reviewers.
     */
    private void sendReviewerNotifications(Long postId, Long hubId, List<Long> reviewerIds, Long accountId) {
        for (Long reviewerId : reviewerIds) {
            HubParticipant reviewer = participantRepository.findById(reviewerId);
            if (reviewer != null && reviewer.getEmail() != null) {
                // Send notification email (this would require EmailService)
                logger.info("Sent review notification to {} for post {}", reviewer.getEmail(), postId);
            }
        }
    }

    /**
     * Deletes associated media files from S3.
     * Media files are now managed through the post_media junction table.
     */
    private void deleteAssociatedMediaFiles(Post post, Long accountId) {
        // Get media files associated with this post through junction table
        List<Media> postMedia = mediaRepository.findByPostId(post.getId());
        for (Media media : postMedia) {
            try {
                // Generate the full file URL for deletion
                String fileUrl = s3Service.generateFileUrl(media.getS3Bucket(), media.getS3Key());
                s3Service.deleteFile(fileUrl, accountId);
                logger.info("Deleted media file: {}", media.getS3Key());
            } catch (Exception e) {
                logger.warn("Failed to delete media file {}: {}", media.getS3Key(), e.getMessage());
            }
        }
    }

    private PostResponse.PostPermissions calculatePostPermissions(Post post, Long userId, Long accountId) {
        boolean canEdit = canUserEditPost(post, userId);

        // Get user's hub participant info
        Long participantId = postRepository.getUserParticipantId(post.getHubId(), userId);
        HubParticipantRole userRole = postRepository.getUserRoleInHub(post.getHubId(), userId);

        // Check if user has reviewer role
        boolean hasReviewerRole = userRole == HubParticipantRole.admin ||
                                 userRole == HubParticipantRole.reviewer ||
                                 userRole == HubParticipantRole.reviewer_creator;

        // Check if user is assigned as reviewer for this specific post
        boolean isAssignedReviewer = participantId != null &&
                                   postReviewerRepository.isParticipantAssignedAsReviewer(post.getId(), participantId);

        // User can review if they have reviewer role AND are assigned to this post
        boolean canReview = hasReviewerRole && isAssignedReviewer;

        // Get user's review status for this post
        ReviewStatus myReviewStatus = null;
        if (isAssignedReviewer) {
            myReviewStatus = postReviewerRepository.getUserReviewStatus(post.getId(), participantId);
        }

        boolean canComment = true; // All participants can comment
        boolean canAssignReviewers = userRole == HubParticipantRole.admin;

        return postConverter.createPermissions(canEdit, canReview, canComment, canAssignReviewers,
                                             isAssignedReviewer, hasReviewerRole, myReviewStatus,
                                             userRole != null ? userRole.name() : null);
    }

    /**
     * Gets all reviewers assigned to a post with their current review status.
     * Uses bulk loading to avoid N+1 queries.
     */
    private List<PostResponse.PostReviewer> getPostReviewers(Long postId) {
        List<org.jooq.generated.tables.pojos.PostReviewer> postReviewers = postReviewerRepository.findByPostId(postId);

        if (postReviewers.isEmpty()) {
            return List.of();
        }

        // Bulk load participant details to avoid N+1 queries
        List<Long> participantIds = postReviewers.stream()
                .map(org.jooq.generated.tables.pojos.PostReviewer::getParticipantId)
                .distinct()
                .toList();

        Map<Long, HubParticipantRepositoryImpl.ParticipantDetails> participantDetailsMap =
                participantRepository.findParticipantDetailsByIds(participantIds);

        return postReviewers.stream()
                .map(reviewer -> convertToPostReviewer(reviewer, participantDetailsMap))
                .filter(reviewer -> reviewer != null)
                .toList();
    }

    /**
     * Converts a PostReviewer entity to PostResponse.PostReviewer DTO using bulk-loaded data.
     */
    private PostResponse.PostReviewer convertToPostReviewer(
            org.jooq.generated.tables.pojos.PostReviewer reviewer,
            Map<Long, HubParticipantRepositoryImpl.ParticipantDetails> participantDetailsMap) {

        HubParticipantRepositoryImpl.ParticipantDetails participantDetails =
                participantDetailsMap.get(reviewer.getParticipantId());

        if (participantDetails == null) {
            logger.warn("Participant details not found for reviewer: {}", reviewer.getParticipantId());
            return null;
        }

        String name = participantDetails.getName() != null ? participantDetails.getName() :
                     (participantDetails.getEmail() != null ?
                      participantDetails.getEmail().substring(0, participantDetails.getEmail().indexOf('@')) :
                      "Unknown");

        return postConverter.createPostReviewer(
                reviewer.getParticipantId(),
                name,
                participantDetails.getEmail(),
                reviewer.getReviewStatus()
        );
    }

    private List<PostListItemResponse> convertPostsToListItems(List<Post> posts, Long userId, Long accountId) {
        if (posts.isEmpty()) {
            return List.of();
        }

        // Bulk fetch creator details to avoid N+1 queries
        List<Long> creatorParticipantIds = posts.stream()
                .map(Post::getCreatorParticipantId)
                .distinct()
                .toList();

        Map<Long, HubParticipantRepositoryImpl.ParticipantDetails> creatorDetailsMap =
                participantRepository.findParticipantDetailsByIds(creatorParticipantIds);

        // Bulk fetch media for all posts to avoid N+1 queries
        List<Long> postIds = posts.stream().map(Post::getId).toList();
        Map<Long, List<Media>> postMediaMap = bulkLoadPostMedia(postIds);

        // Bulk fetch review counts to avoid N+1 queries
        Map<Long, PostReviewerRepositoryImpl.ReviewCounts> reviewCountsMap =
                postReviewerRepository.bulkCountReviewersByPostIds(postIds);

        return posts.stream()
                .map(post -> buildPostListItem(post, creatorDetailsMap, postMediaMap, reviewCountsMap, userId))
                .toList();
    }

    /**
     * Builds a single PostListItemResponse from bulk-loaded data.
     */
    private PostListItemResponse buildPostListItem(
            Post post,
            Map<Long, HubParticipantRepositoryImpl.ParticipantDetails> creatorDetailsMap,
            Map<Long, List<Media>> postMediaMap,
            Map<Long, PostReviewerRepositoryImpl.ReviewCounts> reviewCountsMap,
            Long userId) {

        // Get creator details from bulk fetch
        PostListItemResponse.PostCreator creator = createPostCreator(post, creatorDetailsMap);

        // Get media from bulk fetch
        List<Media> mediaList = postMediaMap.getOrDefault(post.getId(), List.of());

        // Get review counts from bulk fetch
        PostReviewerRepositoryImpl.ReviewCounts reviewCounts =
                reviewCountsMap.getOrDefault(post.getId(), new PostReviewerRepositoryImpl.ReviewCounts(0, 0, 0));

        // Calculate permission data for list item
        Long participantId = postRepository.getUserParticipantId(post.getHubId(), userId);
        HubParticipantRole userRole = postRepository.getUserRoleInHub(post.getHubId(), userId);

        boolean hasReviewerRole = userRole == HubParticipantRole.admin ||
                                 userRole == HubParticipantRole.reviewer ||
                                 userRole == HubParticipantRole.reviewer_creator;

        boolean isAssignedReviewer = participantId != null &&
                                   postReviewerRepository.isParticipantAssignedAsReviewer(post.getId(), participantId);

        boolean canReview = hasReviewerRole && isAssignedReviewer;

        return postConverter.toListItemWithPresignedUrls(
                post, mediaList, creator, post.getReviewStatus(),
                reviewCounts.getTotal(), reviewCounts.getApproved(), reviewCounts.getRework(),
                canUserEditPost(post, userId), canReview, true,
                isAssignedReviewer, hasReviewerRole, userRole != null ? userRole.name() : null
        );
    }

    /**
     * Creates a PostCreator with fallback handling for missing creator details.
     */
    private PostListItemResponse.PostCreator createPostCreator(
            Post post,
            Map<Long, HubParticipantRepositoryImpl.ParticipantDetails> creatorDetailsMap) {

        HubParticipantRepositoryImpl.ParticipantDetails creatorDetails =
                creatorDetailsMap.get(post.getCreatorParticipantId());

        if (creatorDetails != null) {
            return postConverter.createListItemCreator(
                    creatorDetails.getId(),
                    creatorDetails.getName(),
                    creatorDetails.getEmail());
        } else {
            // Fallback for missing creator
            logger.warn("Creator details not found for participant: {}", post.getCreatorParticipantId());
            return postConverter.createListItemCreator(
                    post.getCreatorParticipantId(), "Unknown Creator", "<EMAIL>");
        }
    }

    private PostListResponse.PostFilters createFilterInfo(String currentFilter, ReviewStatus currentStatus,
                                                         HubParticipantRole userRole) {
        List<String> availableFilters = PostFilter.getAllValues();
        List<ReviewStatus> availableStatuses = Arrays.asList(ReviewStatus.values());

        return new PostListResponse.PostFilters(availableFilters, currentFilter, availableStatuses, currentStatus);
    }



    /**
     * Creates or validates media records from URLs and returns their IDs.
     */
    private List<Long> createOrValidateMediaRecords(List<String> mediaUris, Long accountId) {
        logger.debug("Creating/validating {} media records for account {}", mediaUris.size(), accountId);

        return mediaUris.stream()
                .map(uri -> {
                    try {
                        logger.debug("Processing media URI: {}", uri);

                        // Try to create media record from URL (will return existing if already exists)
                        MediaDto mediaDto = mediaService.createMediaFromUrl(
                                uri,
                                extractFilenameFromUrl(uri),
                                0L, // Size unknown from URL
                                determineMimeTypeFromUrl(uri),
                                accountId
                        );

                        logger.debug("Successfully processed media URI: {} -> ID: {}", uri, mediaDto.getId());
                        return mediaDto.getId();
                    } catch (Exception e) {
                        logger.error("Failed to create/validate media record for URL: {} - Error: {}", uri, e.getMessage(), e);
                        throw new BadRequestException(ErrorCode.INVALID_INPUT,
                            PostConstants.INVALID_MEDIA_FILE_MESSAGE + ": " + uri);
                    }
                })
                .toList();
    }

    /**
     * Extracts filename from URL.
     */
    private String extractFilenameFromUrl(String url) {
        try {
            String[] parts = url.split("/");
            return parts[parts.length - 1];
        } catch (Exception e) {
            return "unknown_file";
        }
    }

    /**
     * Determines MIME type from URL extension.
     */
    private String determineMimeTypeFromUrl(String url) {
        String lowerUrl = url.toLowerCase();
        if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerUrl.endsWith(".png")) {
            return "image/png";
        } else if (lowerUrl.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerUrl.endsWith(".mp4")) {
            return "video/mp4";
        } else if (lowerUrl.endsWith(".mov")) {
            return "video/quicktime";
        } else if (lowerUrl.endsWith(".avi")) {
            return "video/x-msvideo";
        }
        return "application/octet-stream"; // Default fallback
    }

    /**
     * Bulk loads media for multiple posts to avoid N+1 queries.
     * Uses a single optimized query instead of multiple individual calls.
     */
    private Map<Long, List<Media>> bulkLoadPostMedia(List<Long> postIds) {
        return mediaService.bulkFindMediaByPostIds(postIds);
    }
}
