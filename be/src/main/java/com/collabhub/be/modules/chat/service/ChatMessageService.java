package com.collabhub.be.modules.chat.service;

import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.chat.converter.ChatConverter;
import com.collabhub.be.modules.chat.dto.*;
import com.collabhub.be.modules.chat.repository.ChatChannelRepositoryImpl;
import com.collabhub.be.modules.chat.repository.ChatMessageRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.media.service.MediaService;
import com.collabhub.be.modules.media.repository.ChatMessageMediaRepositoryImpl;
import com.collabhub.be.modules.media.dto.MediaDto;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.ChatChannel;
import org.jooq.generated.tables.pojos.ChatMessage;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing chat messages, including sending, editing, and retrieving messages.
 */
@Service
public class ChatMessageService {

    private static final Logger logger = LoggerFactory.getLogger(ChatMessageService.class);

    // Pagination constants
    private static final int DEFAULT_MESSAGE_LIMIT = 50;
    private static final int MAX_MESSAGE_LIMIT = 100;

    // File type constants
    private static final Map<String, String> FILE_EXTENSION_MIME_TYPES = Map.of(
        ".jpg", "image/jpeg",
        ".jpeg", "image/jpeg",
        ".png", "image/png",
        ".gif", "image/gif",
        ".mp4", "video/mp4",
        ".mov", "video/quicktime",
        ".avi", "video/x-msvideo"
    );
    private static final String DEFAULT_MIME_TYPE = "application/octet-stream";

    private final ChatMessageRepositoryImpl messageRepository;
    private final ChatChannelRepositoryImpl channelRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final ChatConverter chatConverter;
    private final MentionService mentionService;
    private final ChatChannelService channelService;
    private final MediaService mediaService;
    private final ChatMessageMediaRepositoryImpl chatMessageMediaRepository;

    public ChatMessageService(ChatMessageRepositoryImpl messageRepository,
                            ChatChannelRepositoryImpl channelRepository,
                            HubParticipantRepositoryImpl participantRepository,
                            CollaborationHubRepositoryImpl hubRepository,
                            ChatConverter chatConverter,
                            MentionService mentionService,
                            ChatChannelService channelService,
                            MediaService mediaService,
                            ChatMessageMediaRepositoryImpl chatMessageMediaRepository) {
        this.messageRepository = messageRepository;
        this.channelRepository = channelRepository;
        this.participantRepository = participantRepository;
        this.hubRepository = hubRepository;
        this.chatConverter = chatConverter;
        this.mentionService = mentionService;
        this.channelService = channelService;
        this.mediaService = mediaService;
        this.chatMessageMediaRepository = chatMessageMediaRepository;
    }

    /**
     * Sends a new chat message to a channel by email.
     *
     * @param channelId the channel ID
     * @param senderEmail the sender email
     * @param request the message request
     * @return the created message response
     */
    @Transactional
    public ChatMessageResponse sendMessage(Long channelId, String senderEmail, ChatMessageRequest request) {
        Long participantId = resolveParticipantIdByEmail(senderEmail);
        return sendMessage(channelId, participantId, request);
    }

    /**
     * Sends a new chat message to a channel.
     *
     * @param channelId the channel ID
     * @param participantId the sender participant ID
     * @param request the message request
     * @return the created message response
     */
    @Transactional
    public ChatMessageResponse sendMessage(Long channelId, Long participantId, ChatMessageRequest request) {
        logger.info("Sending message to channel {} from participant {}", channelId, participantId);

        validateMessageRequest(request);
        validateChannelAccess(channelId, participantId);

        HubParticipant participant = getParticipantById(participantId);
        ChatChannel channel = getChannelById(channelId);

        return createMessageWithMedia(channelId, participantId, request, participant, channel);
    }

    /**
     * Validates that a message request has either content or attachments.
     */
    private void validateMessageRequest(ChatMessageRequest request) {
        boolean hasContent = request.getContent() != null && !request.getContent().trim().isEmpty();
        boolean hasAttachments = hasAttachments(request);

        if (!hasContent && !hasAttachments) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Message must have either content or attachments");
        }
    }

    /**
     * Validates that a participant has write access to a channel.
     */
    private void validateChannelAccess(Long channelId, Long participantId) {
        if (!channelService.canParticipantWriteToChannel(channelId, participantId)) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, "Access denied to chat channel");
        }
    }

    /**
     * Creates a message with media attachments.
     */
    private ChatMessageResponse createMessageWithMedia(Long channelId, Long participantId,
                                                     ChatMessageRequest request, HubParticipant participant,
                                                     ChatChannel channel) {
        List<MentionDto> mentions = mentionService.parseMentions(request.getContent(), channel.getHubId());
        List<Long> mediaIds = processAttachmentMedia(request, participant.getHubId());
        JSONB mentionsJsonb = chatConverter.mentionsToJsonb(mentions);

        ChatMessage message = messageRepository.createMessage(
                channelId, participantId, request.getContent(), mentionsJsonb);

        if (mediaIds != null && !mediaIds.isEmpty()) {
            chatMessageMediaRepository.createChatMessageMediaAssociations(message.getId(), mediaIds);
        }

        channelService.updateChannelActivity(channelId);

        List<Media> mediaList = mediaIds != null && !mediaIds.isEmpty() ?
            mediaService.findMediaByChatMessageId(message.getId()) : List.of();
        ChatParticipantDto senderDto = chatConverter.toParticipantDto(participant);
        ChatMessageResponse response = chatConverter.toMessageResponse(message, mediaList, senderDto);

        logger.info("Successfully sent message {} to channel {} from participant {}",
                message.getId(), channelId, participantId);

        return response;
    }

    /**
     * Gets messages from a channel with pagination for infinite scroll by email.
     *
     * @param channelId the channel ID
     * @param requesterEmail the requesting participant email
     * @param limit the maximum number of messages to return
     * @param beforeMessageId optional message ID for pagination
     * @return paginated message list
     */
    @Transactional(readOnly = true)
    public ChatMessageListResponse getMessages(Long channelId, String requesterEmail,
                                             Integer limit, Long beforeMessageId) {
        Long participantId = resolveParticipantIdByEmail(requesterEmail);
        return getMessages(channelId, participantId, limit, beforeMessageId);
    }

    /**
     * Gets messages from a channel with pagination for infinite scroll.
     *
     * @param channelId the channel ID
     * @param participantId the requesting participant ID
     * @param limit the maximum number of messages to return
     * @param beforeMessageId optional message ID for pagination
     * @return paginated message list
     */
    @Transactional(readOnly = true)
    public ChatMessageListResponse getMessages(Long channelId, Long participantId,
                                             Integer limit, Long beforeMessageId) {
        logger.info("Getting messages from channel {} for participant {}", channelId, participantId);

        HubParticipant participant = getParticipantById(participantId);
        ChatChannel channel = validateChannelAccess(channelId, participantId, participant);
        int messageLimit = validateAndSetLimit(limit);

        List<ChatMessage> messages = messageRepository.findMessagesByChannelWithPagination(
                channelId, messageLimit, beforeMessageId);

        List<ChatMessageResponse> messageResponses = convertToMessageResponsesBulk(messages);
        ChatMessageListResponse.ChatChannelInfo channelInfo = createChannelInfo(channel);
        boolean hasMore = messages.size() == messageLimit;

        Long totalMessages = messageRepository.countMessagesByChannel(channelId);
        PageRequest pageRequest = PageRequest.of(0, messageLimit);
        PageResponse<ChatMessageResponse> pageResponse = new PageResponse<>(messageResponses, pageRequest, totalMessages);

        logger.info("Retrieved {} messages from channel {} for participant {}",
                messageResponses.size(), channelId, participantId);

        return new ChatMessageListResponse(messageResponses, pageResponse, channelInfo, hasMore);
    }

    /**
     * Validates channel access and returns the channel.
     */
    private ChatChannel validateChannelAccess(Long channelId, Long participantId, HubParticipant participant) {
        Optional<ChatChannel> channel = channelRepository.findAccessibleChannelById(
                channelId, participantId, participant.getRole());
        if (channel.isEmpty()) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, "Access denied to chat channel");
        }
        return channel.get();
    }

    /**
     * Validates and sets the message limit.
     */
    private int validateAndSetLimit(Integer limit) {
        return limit != null ? Math.min(limit, MAX_MESSAGE_LIMIT) : DEFAULT_MESSAGE_LIMIT;
    }

    /**
     * Creates channel info DTO.
     */
    private ChatMessageListResponse.ChatChannelInfo createChannelInfo(ChatChannel channel) {
        return new ChatMessageListResponse.ChatChannelInfo(
                channel.getId(),
                channel.getName(),
                channel.getScope());
    }

    /**
     * Updates an existing chat message by email.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param authorEmail the participant email (must be message author)
     * @param request the update request
     * @return the updated message response
     */
    @Transactional
    public ChatMessageResponse updateMessage(Long channelId, Long messageId, String authorEmail,
                                           ChatMessageUpdateRequest request) {
        Long participantId = resolveParticipantIdByEmail(authorEmail);
        return updateMessage(channelId, messageId, participantId, request);
    }

    /**
     * Updates an existing chat message.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param participantId the requesting participant ID
     * @param request the update request
     * @return the updated message response
     */
    @Transactional
    public ChatMessageResponse updateMessage(Long channelId, Long messageId, Long participantId,
                                           ChatMessageUpdateRequest request) {
        logger.info("Updating message {} in channel {} by participant {}", messageId, channelId, participantId);

        validateMessageUpdateRequest(request);
        ChatMessage message = validateMessageOwnership(messageId, channelId, participantId);
        ChatChannel channel = getChannelById(channelId);

        return processMessageUpdate(messageId, channelId, request, channel);
    }

    /**
     * Validates that a message update request has either content or attachments.
     */
    private void validateMessageUpdateRequest(ChatMessageUpdateRequest request) {
        boolean hasContent = request.getContent() != null && !request.getContent().trim().isEmpty();
        boolean hasAttachments = hasAttachmentsUpdate(request);

        if (!hasContent && !hasAttachments) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Message must have either content or attachments");
        }
    }

    /**
     * Validates that the message exists and belongs to the participant.
     */
    private ChatMessage validateMessageOwnership(Long messageId, Long channelId, Long participantId) {
        ChatMessage message = messageRepository.findMessageByIdAndChannel(messageId, channelId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.CHAT_MESSAGE_NOT_FOUND,
                        "Message not found"));

        if (!message.getParticipantId().equals(participantId)) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED,
                    "You can only edit your own messages");
        }

        return message;
    }

    /**
     * Processes the message update with mentions and media.
     */
    private ChatMessageResponse processMessageUpdate(Long messageId, Long channelId,
                                                    ChatMessageUpdateRequest request, ChatChannel channel) {
        List<MentionDto> mentions = mentionService.parseMentions(request.getContent(), channel.getHubId());

        if (request.getAttachmentUris() != null || request.getAttachments() != null) {
            List<Long> mediaIds = processAttachmentMediaFromUpdate(request, channel.getHubId());
            chatMessageMediaRepository.updateChatMessageMediaAssociations(messageId, mediaIds);
        }

        JSONB mentionsJsonb = chatConverter.mentionsToJsonb(mentions);
        boolean updated = messageRepository.updateMessage(messageId, request.getContent(), mentionsJsonb);

        if (!updated) {
            throw new NotFoundException(ErrorCode.CHAT_MESSAGE_NOT_FOUND, "Message not found");
        }

        ChatMessage updatedMessage = messageRepository.findMessageByIdAndChannel(messageId, channelId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.CHAT_MESSAGE_NOT_FOUND,
                        "Updated message not found"));

        ChatMessageResponse response = convertToMessageResponse(updatedMessage);

        logger.info("Successfully updated message {} in channel {}", messageId, channelId);
        return response;
    }

    /**
     * Deletes a chat message by email.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param authorEmail the participant email (must be message author)
     */
    @Transactional
    public void deleteMessage(Long channelId, Long messageId, String authorEmail) {
        Long participantId = resolveParticipantIdByEmail(authorEmail);
        deleteMessage(channelId, messageId, participantId);
    }

    /**
     * Deletes a chat message.
     *
     * @param channelId the channel ID
     * @param messageId the message ID
     * @param participantId the requesting participant ID
     */
    @Transactional
    public void deleteMessage(Long channelId, Long messageId, Long participantId) {
        logger.info("Deleting message {} in channel {} by participant {}", messageId, channelId, participantId);

        validateMessageOwnership(messageId, channelId, participantId);

        boolean deleted = messageRepository.deleteMessage(messageId);
        if (!deleted) {
            throw new NotFoundException(ErrorCode.CHAT_MESSAGE_NOT_FOUND, "Message not found");
        }

        logger.info("Successfully deleted message {} in channel {}", messageId, channelId);
    }

    /**
     * Converts a ChatMessage entity to response DTO with sender details and media.
     * This method has N+1 query issue - use convertToMessageResponsesBulk for lists.
     */
    private ChatMessageResponse convertToMessageResponse(ChatMessage message) {
        HubParticipant sender = participantRepository.findById(message.getParticipantId());
        List<Media> mediaList = mediaService.findMediaByChatMessageId(message.getId());

        ChatParticipantDto senderDto = sender != null ? chatConverter.toParticipantDto(sender) : null;
        return chatConverter.toMessageResponse(message, mediaList, senderDto);
    }

    /**
     * Converts a list of ChatMessage entities to response DTOs with bulk loading to avoid N+1 queries.
     */
    private List<ChatMessageResponse> convertToMessageResponsesBulk(List<ChatMessage> messages) {
        if (messages.isEmpty()) {
            return List.of();
        }

        // Bulk load participants to avoid N+1 queries
        List<Long> participantIds = messages.stream()
                .map(ChatMessage::getParticipantId)
                .distinct()
                .toList();

        Map<Long, HubParticipant> participantMap = participantRepository.findByIds(participantIds)
                .stream()
                .collect(Collectors.toMap(HubParticipant::getId, p -> p));

        // Bulk load media for all messages
        List<Long> messageIds = messages.stream()
                .map(ChatMessage::getId)
                .toList();

        Map<Long, List<Media>> mediaMap = mediaService.findMediaByChatMessageIds(messageIds);

        // Convert to response DTOs
        return messages.stream()
                .map(message -> {
                    HubParticipant sender = participantMap.get(message.getParticipantId());
                    List<Media> mediaList = mediaMap.getOrDefault(message.getId(), List.of());
                    ChatParticipantDto senderDto = sender != null ? chatConverter.toParticipantDto(sender) : null;
                    return chatConverter.toMessageResponse(message, mediaList, senderDto);
                })
                .toList();
    }

    /**
     * Gets a participant by ID.
     */
    private HubParticipant getParticipantById(Long participantId) {
        return participantRepository.findOptionalById(participantId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        "Participant not found"));
    }

    /**
     * Gets a channel by ID.
     */
    private ChatChannel getChannelById(Long channelId) {
        ChatChannel channel = channelRepository.fetchOneById(channelId);
        if (channel == null) {
            throw new NotFoundException(ErrorCode.CHAT_CHANNEL_NOT_FOUND, "Chat channel not found");
        }
        return channel;
    }

    /**
     * Checks if a message request has attachments.
     */
    private boolean hasAttachments(ChatMessageRequest request) {
        return (request.getAttachments() != null && !request.getAttachments().isEmpty()) ||
               (request.getAttachmentUris() != null && !request.getAttachmentUris().isEmpty());
    }

    /**
     * Checks if a message update request has attachments.
     */
    private boolean hasAttachmentsUpdate(ChatMessageUpdateRequest request) {
        return (request.getAttachments() != null && !request.getAttachments().isEmpty()) ||
               (request.getAttachmentUris() != null && !request.getAttachmentUris().isEmpty());
    }

    /**
     * Resolves participant ID by email address.
     *
     * @param email the participant email
     * @return the participant ID
     * @throws NotFoundException if participant not found
     */
    private Long resolveParticipantIdByEmail(String email) {
        Optional<HubParticipant> participant = participantRepository.findByEmail(email);
        if (participant.isPresent()) {
            return participant.get().getId();
        }
        throw new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                "Participant not found for email: " + email);
    }

    /**
     * Processes attachment media from a chat message request.
     */
    private List<Long> processAttachmentMedia(ChatMessageRequest request, Long hubId) {
        if (!hasAttachments(request)) {
            return List.of();
        }

        List<String> attachmentUris = extractAttachmentUris(request);
        return createMediaRecordsFromUris(attachmentUris, hubId);
    }

    /**
     * Processes attachment media from a chat message update request.
     */
    private List<Long> processAttachmentMediaFromUpdate(ChatMessageUpdateRequest request, Long hubId) {
        if (!hasAttachmentsUpdate(request)) {
            return List.of();
        }

        List<String> attachmentUris = extractAttachmentUrisFromUpdate(request);
        return createMediaRecordsFromUris(attachmentUris, hubId);
    }

    /**
     * Extracts attachment URIs from a chat message request.
     */
    private List<String> extractAttachmentUris(ChatMessageRequest request) {
        if (request.getAttachmentUris() != null && !request.getAttachmentUris().isEmpty()) {
            return request.getAttachmentUris();
        } else if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
            return request.getAttachments().stream()
                    .map(AttachmentRequestDto::getUrl)
                    .toList();
        }
        return List.of();
    }

    /**
     * Extracts attachment URIs from a chat message update request.
     */
    private List<String> extractAttachmentUrisFromUpdate(ChatMessageUpdateRequest request) {
        if (request.getAttachmentUris() != null && !request.getAttachmentUris().isEmpty()) {
            return request.getAttachmentUris();
        } else if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
            return request.getAttachments().stream()
                    .map(AttachmentRequestDto::getUrl)
                    .toList();
        }
        return List.of();
    }

    /**
     * Creates media records from attachment URIs.
     */
    private List<Long> createMediaRecordsFromUris(List<String> attachmentUris, Long hubId) {
        if (attachmentUris.isEmpty()) {
            return List.of();
        }

        Long accountId = extractAccountIdFromContext(hubId);

        return attachmentUris.stream()
                .map(uri -> createMediaRecordFromUri(uri, accountId))
                .toList();
    }

    /**
     * Creates a media record from a single URI.
     */
    private Long createMediaRecordFromUri(String uri, Long accountId) {
        try {
            MediaDto mediaDto = mediaService.createMediaFromUrl(
                    uri,
                    extractFilenameFromUrl(uri),
                    0L, // Size unknown from URL
                    determineMimeTypeFromUrl(uri),
                    accountId
            );
            return mediaDto.getId();
        } catch (Exception e) {
            logger.error("Failed to create/validate media record for attachment: {}", uri, e);
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Invalid or inaccessible attachment file: " + uri);
        }
    }

    /**
     * Extracts account ID from hub context.
     */
    private Long extractAccountIdFromContext(Long hubId) {
        Long accountId = hubRepository.getAccountIdForHub(hubId);
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Cannot determine account ID for hub: " + hubId);
        }
        return accountId;
    }

    /**
     * Extracts filename from URL.
     */
    private String extractFilenameFromUrl(String url) {
        try {
            String[] parts = url.split("/");
            return parts[parts.length - 1];
        } catch (Exception e) {
            return "unknown_file";
        }
    }

    /**
     * Determines MIME type from URL extension using predefined mappings.
     */
    private String determineMimeTypeFromUrl(String url) {
        String lowerUrl = url.toLowerCase();

        for (Map.Entry<String, String> entry : FILE_EXTENSION_MIME_TYPES.entrySet()) {
            if (lowerUrl.endsWith(entry.getKey())) {
                return entry.getValue();
            }
        }

        return DEFAULT_MIME_TYPE;
    }
}
