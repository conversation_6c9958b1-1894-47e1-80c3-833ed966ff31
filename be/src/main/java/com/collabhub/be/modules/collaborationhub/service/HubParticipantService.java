package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.*;
import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.auth.service.EmailService;
import com.collabhub.be.modules.brands.repository.BrandContactRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.converter.HubParticipantConverter;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.chat.service.ChatChannelService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.BrandContact;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Base64;

/**
 * Service for managing collaboration hub participants.
 * Handles invitation, role management, and access control for hub participants.
 */
@Service
@Transactional
public class HubParticipantService {

    private static final Logger logger = LoggerFactory.getLogger(HubParticipantService.class);
    private static final SecureRandom secureRandom = new SecureRandom();
    private static final int MAGIC_LINK_EXPIRY_DAYS = 7;
    private static final String PARTICIPANT_NOT_FOUND_MESSAGE = "Participant not found with ID: ";
    private static final String INSUFFICIENT_PERMISSIONS_MESSAGE = "Required role: ";
    private static final String INVALID_OPERATION_MESSAGE = "Cannot remove the last admin from the hub";
    private static final String FAILED_EMAIL_MESSAGE = "Failed to send invitation email to ";
    private static final String REACTIVATION_FAILED_MESSAGE = "Failed to reactivate participant";

    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final UserRepository userRepository;
    private final BrandContactRepositoryImpl brandContactRepository;
    private final HubParticipantConverter participantConverter;
    private final EmailService emailService;
    private final ChatChannelService chatChannelService;

    public HubParticipantService(HubParticipantRepositoryImpl participantRepository,
                               CollaborationHubRepositoryImpl hubRepository,
                               UserRepository userRepository,
                               BrandContactRepositoryImpl brandContactRepository,
                               HubParticipantConverter participantConverter,
                               EmailService emailService,
                               ChatChannelService chatChannelService) {
        this.participantRepository = participantRepository;
        this.hubRepository = hubRepository;
        this.userRepository = userRepository;
        this.brandContactRepository = brandContactRepository;
        this.participantConverter = participantConverter;
        this.emailService = emailService;
        this.chatChannelService = chatChannelService;
    }

    /**
     * Invites participants to a collaboration hub.
     *
     * @param hubId the hub ID
     * @param request the invitation request
     * @param accountId the account ID for multi-tenancy
     * @param inviterUserId the user ID of the person sending invitations
     * @return response with invited participants
     */
    public HubParticipantInviteResponse inviteParticipants(Long hubId, HubParticipantInviteRequest request,
                                                         Long accountId, Long inviterUserId) {
        logger.info("Inviting {} participants to hub {} by user {}",
                   request.getParticipants().size(), hubId, inviterUserId);

        CollaborationHub hub = validateHubAccess(hubId, accountId, inviterUserId, HubParticipantRole.admin);
        List<HubParticipantInviteResponse.InvitedParticipant> invitedParticipants = processAllInvitations(hub, request, accountId, inviterUserId);

        logInvitationResults(hubId, accountId, inviterUserId, invitedParticipants, request);
        return new HubParticipantInviteResponse(invitedParticipants);
    }

    /**
     * Gets a paginated list of hub participants with filtering and role-based visibility.
     *
     * @param hubId the hub ID
     * @param pageRequest pagination parameters
     * @param role optional role filter
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return paginated list of participants
     */
    @Transactional(readOnly = true)
    public HubParticipantListResponse getHubParticipants(Long hubId, PageRequest pageRequest,
                                                       HubParticipantRole role,
                                                       Long accountId, Long userId) {
        logger.debug("Retrieving participants for hub {} with filters: role={}",
                    hubId, role);

        // Validate hub access (any participant can view participant list)
        validateHubAccess(hubId, accountId, userId, null);

        // Get current user's role in the hub for visibility filtering
        HubParticipantRole currentUserRole = getCurrentUserRoleInHub(hubId, userId);
        if (currentUserRole == null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                "User is not a participant in this hub");
        }

        int offset = pageRequest.getPage() * pageRequest.getSize();

        // Get participants with role-based visibility filtering
        List<HubParticipant> participants = participantRepository.findParticipantsWithRoleBasedVisibility(
                hubId, currentUserRole, role, offset, pageRequest.getSize());

        // Get total count for pagination with role-based visibility
        long totalElements = participantRepository.countParticipantsWithRoleBasedVisibility(
                hubId, currentUserRole, role);

        // Convert to response DTOs
        List<HubParticipantResponse> participantResponses = convertParticipantsToResponses(participants);

        // Create filter information
        HubParticipantListResponse.ParticipantFilters filters = createFilterInfo(role);

        return new HubParticipantListResponse(participantResponses, pageRequest, totalElements, filters);
    }

    /**
     * Gets details of a specific participant.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return participant details
     */
    @Transactional(readOnly = true)
    public HubParticipantResponse getParticipantDetails(Long hubId, Long participantId,
                                                      Long accountId, Long userId) {
        logger.debug("Retrieving participant {} details for hub {}", participantId, hubId);

        // Validate hub access
        validateHubAccess(hubId, accountId, userId, null);

        // Find participant
        HubParticipant participant = participantRepository.findByIdAndHubId(participantId, hubId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Participant not found with ID: " + participantId));

        // Convert to response
        return convertParticipantToResponse(participant);
    }

    /**
     * Updates a participant's role.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param request the role update request
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return updated participant details
     */
    @Transactional
    public HubParticipantResponse updateParticipantRole(Long hubId, Long participantId,
                                                      HubParticipantUpdateRoleRequest request,
                                                      Long accountId, Long userId) {
        logger.info("Updating participant {} role to {} in hub {} by user {}", 
                   participantId, request.getRole(), hubId, userId);

        // Validate hub access (admin required for role changes)
        validateHubAccess(hubId, accountId, userId, HubParticipantRole.admin);

        // Find participant
        HubParticipant participant = participantRepository.findByIdAndHubId(participantId, hubId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Participant not found with ID: " + participantId));

        // Prevent admin from changing their own role if they're the only admin
        if (participant.getUserId() != null && participant.getUserId().equals(userId) &&
            participant.getRole() == HubParticipantRole.admin) {
            long adminCount = participantRepository.countActiveParticipantsByRole(hubId, HubParticipantRole.admin);
            if (adminCount <= 1) {
                throw new BadRequestException(ErrorCode.INVALID_OPERATION,
                    "Cannot change role of the last admin in the hub");
            }
        }

        // Update role
        boolean updated = participantRepository.updateParticipantRole(participantId, hubId, request.getRole());
        if (!updated) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                "Failed to update participant role");
        }

        // Return updated participant
        participant.setRole(request.getRole());
        return convertParticipantToResponse(participant);
    }

    /**
     * Removes a participant from the hub.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void removeParticipant(Long hubId, Long participantId, Long accountId, Long userId) {
        logger.info("Removing participant {} from hub {} by user {}", participantId, hubId, userId);

        // Validate hub access (admin required for removing participants)
        validateHubAccess(hubId, accountId, userId, HubParticipantRole.admin);

        // Find participant
        HubParticipant participant = participantRepository.findByIdAndHubId(participantId, hubId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Participant not found with ID: " + participantId));

        // Prevent admin from removing themselves if they're the only admin
        if (participant.getUserId() != null && participant.getUserId().equals(userId) && 
            participant.getRole() == HubParticipantRole.admin) {
            long adminCount = participantRepository.countActiveParticipantsByRole(hubId, HubParticipantRole.admin);
            if (adminCount <= 1) {
                throw new BadRequestException(ErrorCode.INVALID_OPERATION,
                    "Cannot remove the last admin from the hub");
            }
        }

        // Soft delete participant
        boolean removed = participantRepository.softDeleteParticipant(participantId, hubId);
        if (!removed) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                "Failed to remove participant");
        }

        // Revoke magic link token if external user
        if (participant.getIsExternal() && participant.getMagicLinkToken() != null) {
            participantRepository.revokeMagicLinkToken(participantId);
        }

        logger.info("PARTICIPANT_REMOVED: participantId={}, hubId={}, accountId={}, removedByUserId={}, participantRole={}, wasExternal={}",
                   participantId, hubId, accountId, userId, participant.getRole(), participant.getIsExternal());
    }

    /**
     * Resends invitation to an external participant.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void resendInvitation(Long hubId, Long participantId, Long accountId, Long userId) {
        logger.info("Resending invitation to participant {} in hub {} by user {}", 
                   participantId, hubId, userId);

        // Validate hub access (admin required)
        CollaborationHub hub = validateHubAccess(hubId, accountId, userId, HubParticipantRole.admin);

        // Find participant
        HubParticipant participant = participantRepository.findByIdAndHubId(participantId, hubId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Participant not found with ID: " + participantId));

        // Only external participants can have invitations resent
        if (!participant.getIsExternal()) {
            throw new BadRequestException(ErrorCode.INVALID_OPERATION,
                "Cannot resend invitation to internal participants");
        }

        // Generate new magic link token
        String newToken = generateMagicLinkToken();
        LocalDateTime newExpiry = LocalDateTime.now().plusDays(MAGIC_LINK_EXPIRY_DAYS);

        // Update participant with new token
        participant.setMagicLinkToken(newToken);
        participant.setMagicLinkExpiry(newExpiry);
        participantRepository.update(participant);

        // Send email invitation
        boolean emailSent = emailService.sendMagicLink(participant.getEmail(), newToken, hub.getName());
        if (!emailSent) {
            logger.warn("Failed to send invitation email to participant {}", participantId);
        }

        logger.info("Successfully resent invitation to participant {}", participantId);
    }

    // Private helper methods

    /**
     * Validates hub access and returns the hub if valid.
     */
    private CollaborationHub validateHubAccess(Long hubId, Long accountId, Long userId, HubParticipantRole requiredRole) {
        // Find hub with access validation
        CollaborationHub hub = hubRepository.findByIdWithAccess(hubId, accountId, userId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_NOT_FOUND,
                    "Hub not found or access denied: " + hubId));

        // Check role requirement if specified
        if (requiredRole != null) {
            HubParticipant userParticipant = participantRepository.findByHubIdAndUserId(hubId, userId);
            if (userParticipant == null || userParticipant.getRole() != requiredRole) {
                throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                    "Required role: " + requiredRole);
            }
        }

        return hub;
    }

    /**
     * Gets the current user's role in the specified hub.
     *
     * @param hubId the hub ID
     * @param userId the user ID
     * @return the user's role in the hub, or null if not a participant
     */
    private HubParticipantRole getCurrentUserRoleInHub(Long hubId, Long userId) {
        HubParticipant userParticipant = participantRepository.findByHubIdAndUserId(hubId, userId);
        return userParticipant != null ? userParticipant.getRole() : null;
    }

    /**
     * Processes a single participant invitation.
     */
    private HubParticipantInviteResponse.InvitedParticipant processParticipantInvitation(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId, Long inviterUserId) {

        return switch (item.getType().toLowerCase()) {
            case "internal" -> inviteInternalUser(hub, item, accountId);
            case "external" -> inviteExternalUser(hub, item);
            case "brand_contact" -> inviteBrandContact(hub, item, accountId);
            default -> throw new BadRequestException(ErrorCode.INVALID_INPUT,
                    "Invalid participant type: " + item.getType());
        };
    }

    /**
     * Invites an internal user to the hub.
     */
    private HubParticipantInviteResponse.InvitedParticipant inviteInternalUser(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId) {

        if (item.getUserId() == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "User ID is required for internal participants");
        }

        // Validate user exists and belongs to same account
        User user = userRepository.findById(item.getUserId());
        if (user == null || !user.getAccountId().equals(accountId)) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                "User not found or not in same account: " + item.getUserId());
        }

        // Check if user is already an active participant
        HubParticipant activeParticipant = participantRepository.findByHubIdAndUserId(hub.getId(), item.getUserId());
        if (activeParticipant != null) {
            throw new ConflictException(ErrorCode.RESOURCE_ALREADY_EXISTS,
                "User is already a participant in this hub");
        }

        // Check if user was previously removed and can be reactivated
        HubParticipant existingParticipant = participantRepository.findByHubIdAndUserIdIncludingRemoved(hub.getId(), item.getUserId());
        if (existingParticipant != null && existingParticipant.getRemovedAt() != null) {
            // Reactivate the existing participant
            boolean reactivated = participantRepository.reactivateParticipant(
                    existingParticipant.getId(), item.getRole(), null, null, true);

            if (!reactivated) {
                throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR,
                    "Failed to reactivate participant");
            }

            // Refresh the participant data
            existingParticipant = participantRepository.findById(existingParticipant.getId());

            // Create creator-specific chat channel if participant is a content creator
            if (item.getRole() == HubParticipantRole.content_creator) {
                chatChannelService.createCreatorChannelForParticipant(
                        hub.getId(), existingParticipant.getId(), user.getDisplayName());
            }

            logger.info("Reactivated internal participant {} in hub {}", existingParticipant.getId(), hub.getId());
            return new HubParticipantInviteResponse.InvitedParticipant(
                    existingParticipant.getId(), existingParticipant.getUserId(), existingParticipant.getEmail(),
                    user.getDisplayName(), existingParticipant.getRole(), existingParticipant.getIsExternal(),
                    null, null, existingParticipant.getInvitedAt(), existingParticipant.getJoinedAt(), "active");
        }

        // Create new internal participant
        HubParticipant participant = participantConverter.createInternalParticipant(
                hub.getId(), user.getId(), user.getEmail(), item.getRole());
        participantRepository.insert(participant);

        // Create creator-specific chat channel if participant is a content creator
        if (item.getRole() == HubParticipantRole.content_creator) {
            chatChannelService.createCreatorChannelForParticipant(
                    hub.getId(), participant.getId(), user.getDisplayName());
        }

        // Convert to response
        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), participant.getUserId(), participant.getEmail(),
                user.getDisplayName(), participant.getRole(), participant.getIsExternal(),
                null, null, participant.getInvitedAt(), participant.getJoinedAt(), "active");
    }

    /**
     * Invites an external user to the hub.
     */
    private HubParticipantInviteResponse.InvitedParticipant inviteExternalUser(
            CollaborationHub hub, ParticipantInviteItem item) {

        if (item.getEmail() == null || item.getEmail().trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Email is required for external participants");
        }

        // Check if email is already an active participant
        HubParticipant activeParticipant = participantRepository.findByHubIdAndEmail(hub.getId(), item.getEmail());
        if (activeParticipant != null) {
            throw new ConflictException(ErrorCode.RESOURCE_ALREADY_EXISTS,
                "Email is already a participant in this hub");
        }

        // Generate magic link token
        String magicToken = generateMagicLinkToken();
        LocalDateTime expiry = LocalDateTime.now().plusDays(MAGIC_LINK_EXPIRY_DAYS);

        // Use provided name or derive from email
        String participantName = item.getName() != null ? item.getName() :
                                item.getEmail().substring(0, item.getEmail().indexOf('@'));

        // Check if email was previously removed and can be reactivated
        HubParticipant existingParticipant = participantRepository.findByHubIdAndEmailIncludingRemoved(hub.getId(), item.getEmail());
        if (existingParticipant != null && existingParticipant.getRemovedAt() != null) {
            // Reactivate the existing participant
            boolean reactivated = participantRepository.reactivateParticipant(
                    existingParticipant.getId(), item.getRole(), magicToken, expiry, false);

            if (!reactivated) {
                throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR,
                    "Failed to reactivate participant");
            }

            // Refresh the participant data
            existingParticipant = participantRepository.findById(existingParticipant.getId());

            // Create creator-specific chat channel if participant is a content creator
            if (item.getRole() == HubParticipantRole.content_creator) {
                chatChannelService.createCreatorChannelForParticipant(
                        hub.getId(), existingParticipant.getId(), participantName);
            }

            // Send invitation email
            boolean emailSent = emailService.sendMagicLink(item.getEmail(), magicToken, hub.getName());
            if (!emailSent) {
                logger.warn("Failed to send invitation email to {}", item.getEmail());
            }

            logger.info("Reactivated external participant {} in hub {}", existingParticipant.getId(), hub.getId());
            return new HubParticipantInviteResponse.InvitedParticipant(
                    existingParticipant.getId(), null, existingParticipant.getEmail(), participantName,
                    existingParticipant.getRole(), existingParticipant.getIsExternal(), magicToken, expiry,
                    existingParticipant.getInvitedAt(), null, "pending");
        }

        // Create new external participant
        HubParticipant participant = participantConverter.createExternalParticipant(
                hub.getId(), item.getEmail(), item.getRole(), magicToken, expiry);
        participantRepository.insert(participant);

        // Create creator-specific chat channel if participant is a content creator
        if (item.getRole() == HubParticipantRole.content_creator) {
            chatChannelService.createCreatorChannelForParticipant(
                    hub.getId(), participant.getId(), participantName);
        }

        // Send invitation email
        boolean emailSent = emailService.sendMagicLink(item.getEmail(), magicToken, hub.getName());
        if (!emailSent) {
            logger.warn("Failed to send invitation email to {}", item.getEmail());
        }

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), null, participant.getEmail(), participantName,
                participant.getRole(), participant.getIsExternal(), magicToken, expiry,
                participant.getInvitedAt(), null, "pending");
    }

    /**
     * Invites a brand contact to the hub.
     */
    private HubParticipantInviteResponse.InvitedParticipant inviteBrandContact(
            CollaborationHub hub, ParticipantInviteItem item, Long accountId) {

        if (item.getBrandContactId() == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Brand contact ID is required for brand contact participants");
        }

        // Find brand contact
        BrandContact contact = brandContactRepository.findByIdAndBrandIdAndAccountId(
                item.getBrandContactId(), hub.getBrandId(), accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                    "Brand contact not found: " + item.getBrandContactId()));

        // Check if contact email is already an active participant
        HubParticipant activeParticipant = participantRepository.findByHubIdAndEmail(hub.getId(), contact.getEmail());
        if (activeParticipant != null) {
            throw new ConflictException(ErrorCode.RESOURCE_ALREADY_EXISTS,
                "Brand contact is already a participant in this hub");
        }

        // Generate magic link token
        String magicToken = generateMagicLinkToken();
        LocalDateTime expiry = LocalDateTime.now().plusDays(MAGIC_LINK_EXPIRY_DAYS);

        // Check if contact email was previously removed and can be reactivated
        HubParticipant existingParticipant = participantRepository.findByHubIdAndEmailIncludingRemoved(hub.getId(), contact.getEmail());
        if (existingParticipant != null && existingParticipant.getRemovedAt() != null) {
            // Reactivate the existing participant
            boolean reactivated = participantRepository.reactivateParticipant(
                    existingParticipant.getId(), item.getRole(), magicToken, expiry, false);

            if (!reactivated) {
                throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR,
                    "Failed to reactivate participant");
            }

            // Refresh the participant data
            existingParticipant = participantRepository.findById(existingParticipant.getId());

            // Create creator-specific chat channel if participant is a content creator
            if (item.getRole() == HubParticipantRole.content_creator) {
                chatChannelService.createCreatorChannelForParticipant(
                        hub.getId(), existingParticipant.getId(), contact.getName());
            }

            // Send invitation email
            boolean emailSent = emailService.sendMagicLink(contact.getEmail(), magicToken, hub.getName());
            if (!emailSent) {
                logger.warn("Failed to send invitation email to brand contact {}", item.getBrandContactId());
            }

            logger.info("Reactivated brand contact participant {} in hub {}", existingParticipant.getId(), hub.getId());
            return new HubParticipantInviteResponse.InvitedParticipant(
                    existingParticipant.getId(), null, existingParticipant.getEmail(), contact.getName(),
                    existingParticipant.getRole(), existingParticipant.getIsExternal(), magicToken, expiry,
                    existingParticipant.getInvitedAt(), null, "pending");
        }

        // Create new external participant using brand contact info
        HubParticipant participant = participantConverter.createExternalParticipant(
                hub.getId(), contact.getEmail(), item.getRole(), magicToken, expiry);
        participantRepository.insert(participant);

        // Create creator-specific chat channel if participant is a content creator
        if (item.getRole() == HubParticipantRole.content_creator) {
            chatChannelService.createCreatorChannelForParticipant(
                    hub.getId(), participant.getId(), contact.getName());
        }

        // Send invitation email
        boolean emailSent = emailService.sendMagicLink(contact.getEmail(), magicToken, hub.getName());
        if (!emailSent) {
            logger.warn("Failed to send invitation email to brand contact {}", item.getBrandContactId());
        }

        return new HubParticipantInviteResponse.InvitedParticipant(
                participant.getId(), null, participant.getEmail(), contact.getName(),
                participant.getRole(), participant.getIsExternal(), magicToken, expiry,
                participant.getInvitedAt(), null, "pending");
    }

    /**
     * Converts participants to response DTOs with name resolution.
     */
    private List<HubParticipantResponse> convertParticipantsToResponses(List<HubParticipant> participants) {
        if (participants == null || participants.isEmpty()) {
            return List.of();
        }

        List<Long> participantIds = participants.stream()
                .map(HubParticipant::getId)
                .collect(Collectors.toList());

        Map<Long, String> participantNames = participantRepository.findParticipantNamesByIds(participantIds);

        return participants.stream()
                .map(participant -> {
                    String name = participantNames.get(participant.getId());
                    if (name == null && participant.getIsExternal()) {
                        // For external users, use email prefix as name if no display name
                        name = participant.getEmail().substring(0, participant.getEmail().indexOf('@'));
                    }
                    String status = determineParticipantStatus(participant);
                    return new HubParticipantResponse(
                            participant.getId(), participant.getUserId(), participant.getEmail(),
                            name, participant.getRole(), participant.getIsExternal(),
                            participant.getInvitedAt(), participant.getJoinedAt(), status);
                })
                .collect(Collectors.toList());
    }

    /**
     * Converts a single participant to response DTO.
     */
    private HubParticipantResponse convertParticipantToResponse(HubParticipant participant) {
        String name = null;
        if (participant.getUserId() != null) {
            // Internal user - get display name from user table
            User user = userRepository.findById(participant.getUserId());
            if (user != null) {
                name = user.getDisplayName();
            }
        } else {
            // External user - use email prefix as name
            name = participant.getEmail().substring(0, participant.getEmail().indexOf('@'));
        }

        String status = determineParticipantStatus(participant);
        return new HubParticipantResponse(
                participant.getId(), participant.getUserId(), participant.getEmail(),
                name, participant.getRole(), participant.getIsExternal(),
                participant.getInvitedAt(), participant.getJoinedAt(), status);
    }

    /**
     * Determines participant status based on their state.
     */
    private String determineParticipantStatus(HubParticipant participant) {
        if (participant.getRemovedAt() != null) {
            return "removed";
        }
        if (participant.getIsExternal() && participant.getJoinedAt() == null) {
            return "pending";
        }
        return "active";
    }

    /**
     * Creates filter information for participant list responses.
     */
    private HubParticipantListResponse.ParticipantFilters createFilterInfo(HubParticipantRole currentRole) {
        List<String> availableStatuses = Arrays.asList(); // Empty list since status filtering is removed
        List<HubParticipantRole> availableRoles = Arrays.asList(HubParticipantRole.values());

        return new HubParticipantListResponse.ParticipantFilters(
                availableStatuses, availableRoles, null, currentRole);
    }

    /**
     * Generates a secure magic link token.
     */
    private String generateMagicLinkToken() {
        String token;
        int attempts = 0;
        final int maxAttempts = 10;

        do {
            byte[] tokenBytes = new byte[32];
            secureRandom.nextBytes(tokenBytes);
            token = Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
            attempts++;

            if (attempts >= maxAttempts) {
                logger.error("Failed to generate unique magic link token after {} attempts", maxAttempts);
                throw new RuntimeException("Unable to generate unique magic link token");
            }
        } while (isTokenAlreadyExists(token));

        return token;
    }

    /**
     * Checks if a magic link token already exists in the database.
     */
    private boolean isTokenAlreadyExists(String token) {
        return participantRepository.existsByMagicLinkToken(token);
    }

    /**
     * Processes all participant invitations in the request.
     */
    private List<HubParticipantInviteResponse.InvitedParticipant> processAllInvitations(
            CollaborationHub hub, HubParticipantInviteRequest request, Long accountId, Long inviterUserId) {

        List<HubParticipantInviteResponse.InvitedParticipant> invitedParticipants = new ArrayList<>();

        for (ParticipantInviteItem item : request.getParticipants()) {
            try {
                HubParticipantInviteResponse.InvitedParticipant invited = processParticipantInvitation(
                        hub, item, accountId, inviterUserId);
                invitedParticipants.add(invited);
            } catch (Exception e) {
                logger.warn("Failed to invite participant {}: {}", item, e.getMessage());
                // Continue with other participants, but could collect errors for response
            }
        }

        return invitedParticipants;
    }

    /**
     * Logs the results of participant invitations.
     */
    private void logInvitationResults(Long hubId, Long accountId, Long inviterUserId,
                                    List<HubParticipantInviteResponse.InvitedParticipant> invitedParticipants,
                                    HubParticipantInviteRequest request) {
        logger.info("PARTICIPANTS_INVITED: hubId={}, accountId={}, inviterUserId={}, participantCount={}, types={}",
                   hubId, accountId, inviterUserId, invitedParticipants.size(),
                   request.getParticipants().stream().map(ParticipantInviteItem::getType).distinct().toList());
    }
}
