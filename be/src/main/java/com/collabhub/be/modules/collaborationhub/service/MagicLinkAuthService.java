package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for handling magic link authentication for external hub participants.
 * Validates magic link tokens and manages external user access to collaboration hubs.
 */
@Service
public class MagicLinkAuthService {

    private static final Logger logger = LoggerFactory.getLogger(MagicLinkAuthService.class);

    private final HubParticipantRepositoryImpl participantRepository;

    public MagicLinkAuthService(HubParticipantRepositoryImpl participantRepository) {
        this.participantRepository = participantRepository;
    }

    /**
     * Validates a magic link token and returns participant information.
     * Marks the participant as joined if this is their first access.
     *
     * @param token the magic link token
     * @return participant information if token is valid
     * @throws NotFoundException if token is invalid or expired
     */
    @Transactional
    public MagicLinkValidationResult validateMagicLinkToken(String token) {
        logger.info("Validating magic link token: {}", token.substring(0, Math.min(8, token.length())) + "...");

        if (token == null || token.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Magic link token is required");
        }

        // Find participant by token
        HubParticipant participant = participantRepository.findByMagicLinkToken(token)
                .orElseThrow(() -> new NotFoundException(ErrorCode.INVALID_TOKEN,
                    "Invalid or expired magic link token"));

        // Mark participant as joined if this is their first access
        boolean isFirstAccess = participant.getJoinedAt() == null;
        if (isFirstAccess) {
            boolean updated = participantRepository.markParticipantAsJoined(participant.getId());
            if (updated) {
                logger.info("Marked participant {} as joined for first time", participant.getId());
            }
        }

        logger.info("Successfully validated magic link token for participant {} in hub {}", 
                   participant.getId(), participant.getHubId());

        return new MagicLinkValidationResult(
                participant.getId(),
                participant.getHubId(),
                participant.getEmail(),
                participant.getRole(),
                isFirstAccess
        );
    }

    /**
     * Revokes a magic link token, preventing further access.
     *
     * @param participantId the participant ID
     * @return true if token was revoked successfully
     */
    @Transactional
    public boolean revokeMagicLinkToken(Long participantId) {
        logger.info("Revoking magic link token for participant {}", participantId);

        boolean revoked = participantRepository.revokeMagicLinkToken(participantId);
        if (revoked) {
            logger.info("Successfully revoked magic link token for participant {}", participantId);
        } else {
            logger.warn("Failed to revoke magic link token for participant {}", participantId);
        }

        return revoked;
    }

    /**
     * Result of magic link token validation.
     */
    public static class MagicLinkValidationResult {
        private final Long participantId;
        private final Long hubId;
        private final String email;
        private final org.jooq.generated.enums.HubParticipantRole role;
        private final boolean isFirstAccess;

        public MagicLinkValidationResult(Long participantId, Long hubId, String email,
                                       org.jooq.generated.enums.HubParticipantRole role, boolean isFirstAccess) {
            this.participantId = participantId;
            this.hubId = hubId;
            this.email = email;
            this.role = role;
            this.isFirstAccess = isFirstAccess;
        }

        public Long getParticipantId() {
            return participantId;
        }

        public Long getHubId() {
            return hubId;
        }

        public String getEmail() {
            return email;
        }

        public org.jooq.generated.enums.HubParticipantRole getRole() {
            return role;
        }

        public boolean isFirstAccess() {
            return isFirstAccess;
        }

        @Override
        public String toString() {
            return "MagicLinkValidationResult{" +
                    "participantId=" + participantId +
                    ", hubId=" + hubId +
                    ", email='" + email + '\'' +
                    ", role=" + role +
                    ", isFirstAccess=" + isFirstAccess +
                    '}';
        }
    }
}
