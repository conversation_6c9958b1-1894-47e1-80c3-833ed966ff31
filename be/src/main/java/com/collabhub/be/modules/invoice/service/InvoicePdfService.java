package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.InternalServerErrorException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.bankdetails.repository.BankDetailsRepositoryImpl;
import com.collabhub.be.modules.brands.repository.BrandRepositoryImpl;
import com.collabhub.be.modules.companies.repository.AccountCompanyRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.InvoiceResponse;
import com.collabhub.be.modules.invoice.dto.InvoiceSnapshotResponse;
import com.collabhub.be.modules.invoice.repository.InvoiceRepositoryImpl;
import com.collabhub.be.modules.invoice.service.InvoiceValidationService;
import com.collabhub.be.modules.invoice.model.InvoiceSnapshot;
import com.collabhub.be.util.JsonbUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.source.ByteArrayOutputStream;
import org.jooq.JSONB;
import org.jooq.generated.enums.InvoiceStatus;
import org.jooq.generated.tables.pojos.Invoice;
import org.jooq.generated.tables.pojos.BankDetails;
import org.jooq.generated.tables.pojos.Brand;
import org.jooq.generated.tables.pojos.AccountCompany;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.ByteArrayInputStream;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Map;

/**
 * Service for generating PDF invoices using HTML templates and iText PDF conversion.
 * Supports multiple template variants and professional invoice formatting.
 */
@Service
public class InvoicePdfService {

    private static final Logger logger = LoggerFactory.getLogger(InvoicePdfService.class);

    private final TemplateEngine templateEngine;
    private final InvoiceCalculationService calculationService;
    private final InvoiceSnapshotService snapshotService;
    private final AccountCompanyRepositoryImpl accountCompanyRepository;
    private final BrandRepositoryImpl brandRepository;
    private final BankDetailsRepositoryImpl bankDetailsRepository;
    private final InvoiceService invoiceService;
    private final InvoiceValidationService validationService;
    private final InvoiceRepositoryImpl invoiceRepository;
    private final ObjectMapper objectMapper;

    // Date formatters for different locales
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter DATE_FORMATTER_US = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    public InvoicePdfService(TemplateEngine templateEngine,
                           InvoiceCalculationService calculationService,
                           InvoiceSnapshotService snapshotService,
                           AccountCompanyRepositoryImpl accountCompanyRepository,
                           BrandRepositoryImpl brandRepository,
                           BankDetailsRepositoryImpl bankDetailsRepository,
                           InvoiceService invoiceService,
                           InvoiceValidationService validationService,
                           InvoiceRepositoryImpl invoiceRepository,
                           ObjectMapper objectMapper) {
        this.templateEngine = templateEngine;
        this.calculationService = calculationService;
        this.snapshotService = snapshotService;
        this.accountCompanyRepository = accountCompanyRepository;
        this.brandRepository = brandRepository;
        this.bankDetailsRepository = bankDetailsRepository;
        this.invoiceService = invoiceService;
        this.validationService = validationService;
        this.invoiceRepository = invoiceRepository;
        this.objectMapper = objectMapper;
    }

    /**
     * Generates PDF for an invoice using the default template.
     *
     * @param invoice the invoice data
     * @return PDF as byte array
     * @throws InternalServerErrorException if PDF generation fails
     */
    @Transactional(readOnly = true)
    public byte[] generateInvoicePdf(InvoiceResponse invoice) {
        logger.debug("Generating PDF for invoice {}", invoice.getInvoiceNumber());

        try {
            // Generate HTML content from template
            String htmlContent = generateHtmlContent(invoice);

            // Convert HTML to PDF
            byte[] pdfBytes = convertHtmlToPdf(htmlContent);

            logger.info("Successfully generated PDF for invoice {} ({} bytes)",
                       invoice.getInvoiceNumber(), pdfBytes.length);

            return pdfBytes;

        } catch (Exception e) {
            logger.error("Failed to generate PDF for invoice {}: {}", invoice.getInvoiceNumber(), e.getMessage(), e);
            throw new InternalServerErrorException(ErrorCode.PDF_GENERATION_FAILED,
                    "Failed to generate invoice PDF: " + e.getMessage());
        }
    }

    /**
     * Generates PDF for an invoice with validation using the default template.
     * This method handles fetching the invoice, validating it, and generating the PDF.
     *
     * @param invoiceId the invoice ID
     * @param accountId the account ID for multi-tenancy
     * @return PDF as byte array
     * @throws NotFoundException if the invoice is not found
     * @throws BadRequestException if validation fails
     * @throws InternalServerErrorException if PDF generation fails
     */
    @Transactional(readOnly = true)
    public byte[] generateInvoicePdfWithValidation(Long invoiceId, Long accountId) {
        logger.debug("Generating PDF with validation for invoice ID: {} for account: {}", invoiceId, accountId);

        // Get invoice data
        InvoiceResponse invoice = invoiceService.getInvoiceById(invoiceId, accountId);

        // Validate using the centralized validation service
        validateInvoiceForPdf(invoice, accountId);

        // Generate PDF
        return generateInvoicePdf(invoice);
    }

    /**
     * Validates invoice for PDF generation using the centralized validation service.
     */
    private void validateInvoiceForPdf(InvoiceResponse invoice, Long accountId) {
        // Get the invoice entity for validation
        Invoice invoiceEntity = invoiceRepository.findByIdAndAccountId(invoice.getId(), accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND,
                        InvoiceConstants.INVOICE_NOT_FOUND_MESSAGE));

        // Use centralized validation
        validationService.validateInvoiceForSendingOrPdf(invoiceEntity);
    }

    /**
     * Generates HTML content from Thymeleaf template.
     *
     * @param invoice the invoice data
     * @return HTML content as string
     */
    private String generateHtmlContent(InvoiceResponse invoice) {
        // Create Thymeleaf context with invoice data
        Context context = new Context();

        // Add invoice data
        context.setVariable("invoice", invoice);
        context.setVariable("items", invoice.getItems());
        context.setVariable("recipients", invoice.getRecipients());

        // Fetch snapshot data (for non-DRAFT) or live data (for DRAFT)
        context.setVariable("issuer", fetchIssuerSnapshot(invoice));
        context.setVariable("recipient", fetchRecipientSnapshot(invoice));
        context.setVariable("bankDetails", fetchBankDetailsSnapshot(invoice));

        // Add formatted data
        context.setVariable("formattedIssueDate", formatDate(invoice.getIssueDate()));
        context.setVariable("formattedDueDate", formatDate(invoice.getDueDate()));
        context.setVariable("formattedSubtotal", calculationService.formatCurrency(invoice.getSubtotal(), invoice.getCurrency()));
        context.setVariable("formattedTotalVat", calculationService.formatCurrency(invoice.getTotalVat(), invoice.getCurrency()));
        context.setVariable("formattedTotalAmount", calculationService.formatCurrency(invoice.getTotalAmount(), invoice.getCurrency()));

        // Process the template
        String templatePath = "invoice/pdf/default";
        return templateEngine.process(templatePath, context);
    }

    /**
     * Converts HTML content to PDF using iText.
     *
     * @param htmlContent the HTML content
     * @return PDF as byte array
     */
    private byte[] convertHtmlToPdf(String htmlContent) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            // Configure converter properties
            ConverterProperties converterProperties = new ConverterProperties();
            converterProperties.setCharset("UTF-8");
            
            // Convert HTML to PDF
            HtmlConverter.convertToPdf(
                new ByteArrayInputStream(htmlContent.getBytes("UTF-8")),
                outputStream,
                converterProperties
            );
            
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert HTML to PDF", e);
        }
    }

    /**
     * Formats date for display.
     *
     * @param date the date to format
     * @return formatted date string
     */
    private String formatDate(java.time.LocalDate date) {
        if (date == null) {
            return "";
        }
        return date.format(DATE_FORMATTER);
    }



    /**
     * Fetches issuer snapshot for PDF generation.
     * For DRAFT invoices, fetches live data from AccountCompany.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse fetchIssuerSnapshot(InvoiceResponse invoice) {
        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            // For DRAFT invoices, fetch live data
            if (invoice.getIssuerId() == null) {
                return new InvoiceSnapshotResponse.IssuerSnapshotResponse();
            }

            return accountCompanyRepository.findByIdAndAccountId(invoice.getIssuerId(), invoice.getAccountId())
                    .map(this::convertAccountCompanyToIssuerResponse)
                    .orElse(new InvoiceSnapshotResponse.IssuerSnapshotResponse());
        } else {
            // For non-DRAFT invoices, fetch snapshot data
            return snapshotService.getIssuerSnapshotByHash(invoice.getIssuerSnapshotHash());
        }
    }

    /**
     * Fetches recipient snapshot for PDF generation.
     * For DRAFT invoices, fetches live data from Brand.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse fetchRecipientSnapshot(InvoiceResponse invoice) {
        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            // For DRAFT invoices, fetch live data
            if (invoice.getRecipientId() == null) {
                return new InvoiceSnapshotResponse.RecipientSnapshotResponse();
            }

            return brandRepository.findByIdAndAccountId(invoice.getRecipientId(), invoice.getAccountId())
                    .map(this::convertBrandToRecipientResponse)
                    .orElse(new InvoiceSnapshotResponse.RecipientSnapshotResponse());
        } else {
            // For non-DRAFT invoices, fetch snapshot data
            return snapshotService.getRecipientSnapshotByHash(invoice.getRecipientSnapshotHash());
        }
    }

    /**
     * Fetches bank details snapshot for PDF generation.
     * For DRAFT invoices, fetches live data from BankDetails.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse fetchBankDetailsSnapshot(InvoiceResponse invoice) {
        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            // For DRAFT invoices, fetch live data
            if (invoice.getBankDetailsId() == null) {
                return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse();
            }

            return bankDetailsRepository.findByIdAndAccountId(invoice.getBankDetailsId(), invoice.getAccountId())
                    .map(this::convertBankDetailsToBankDetailsResponse)
                    .orElse(new InvoiceSnapshotResponse.BankDetailsSnapshotResponse());
        } else {
            // For non-DRAFT invoices, fetch snapshot data
            return snapshotService.getBankDetailsSnapshotByHash(invoice.getBankDetailsSnapshotHash());
        }
    }

    /**
     * Converts AccountCompany to IssuerSnapshotResponse.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse convertAccountCompanyToIssuerResponse(org.jooq.generated.tables.pojos.AccountCompany company) {
        return new InvoiceSnapshotResponse.IssuerSnapshotResponse(
                company.getId(),
                company.getCompanyName(),
                company.getAddressStreet(),
                company.getAddressCity(),
                company.getAddressPostalCode(),
                company.getAddressCountry(),
                company.getVatNumber(),
                company.getRegistrationNumber(),
                company.getPhone(),
                company.getEmail(),
                company.getWebsite()
        );
    }

    /**
     * Converts Brand to RecipientSnapshotResponse.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse convertBrandToRecipientResponse(org.jooq.generated.tables.pojos.Brand brand) {
        return new InvoiceSnapshotResponse.RecipientSnapshotResponse(
                brand.getId(),
                brand.getName(),
                brand.getAddressStreet(),
                brand.getAddressCity(),
                brand.getAddressPostalCode(),
                brand.getAddressCountry(),
                brand.getVatNumber(),
                brand.getRegistrationNumber(),
                brand.getEmail(),
                brand.getPhone()
        );
    }

    /**
     * Converts BankDetails to BankDetailsSnapshotResponse.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse convertBankDetailsToBankDetailsResponse(org.jooq.generated.tables.pojos.BankDetails bankDetails) {
        return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse(
                bankDetails.getId(),
                bankDetails.getName(),
                bankDetails.getBankName(),
                bankDetails.getIban(),
                bankDetails.getBicswift()
        );
    }
}
