package com.collabhub.be.modules.collaborationhub.repository;

import org.jooq.DSLContext;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.daos.CollaborationHubDao;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.jooq.generated.tables.CollaborationHub.COLLABORATION_HUB;
import static org.jooq.generated.tables.HubParticipant.HUB_PARTICIPANT;
import static org.jooq.generated.tables.Brand.BRAND;

/**
 * Repository for CollaborationHub entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy support.
 */
@Repository
public class CollaborationHubRepositoryImpl extends CollaborationHubDao {

    private final DSLContext dsl;

    public CollaborationHubRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds collaboration hubs with filtering and pagination for a specific account.
     * Only returns hubs where the user is a participant.
     *
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID to filter by participation
     * @param nameFilter optional name filter (case-insensitive partial match)
     * @param brandId optional brand filter
     * @param offset the number of records to skip
     * @param limit the maximum number of records to return
     * @return list of collaboration hubs matching the criteria
     */
    public List<CollaborationHub> findHubsWithPagination(Long accountId, Long userId, String nameFilter, 
                                                        Long brandId, int offset, int limit) {
        var query = dsl.select(COLLABORATION_HUB.fields())
                .from(COLLABORATION_HUB)
                .join(HUB_PARTICIPANT).on(HUB_PARTICIPANT.HUB_ID.eq(COLLABORATION_HUB.ID))
                .where(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull());

        // Add name filter if provided
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            query = query.and(COLLABORATION_HUB.NAME.containsIgnoreCase(nameFilter.trim()));
        }

        // Add brand filter if provided
        if (brandId != null) {
            query = query.and(COLLABORATION_HUB.BRAND_ID.eq(brandId));
        }

        return query.orderBy(COLLABORATION_HUB.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(CollaborationHub.class);
    }

    /**
     * Counts collaboration hubs with filtering for a specific account.
     * Only counts hubs where the user is a participant.
     *
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID to filter by participation
     * @param nameFilter optional name filter (case-insensitive partial match)
     * @param brandId optional brand filter
     * @return total count of collaboration hubs matching the criteria
     */
    public long countHubsWithFilter(Long accountId, Long userId, String nameFilter, Long brandId) {
        var query = dsl.selectCount()
                .from(COLLABORATION_HUB)
                .join(HUB_PARTICIPANT).on(HUB_PARTICIPANT.HUB_ID.eq(COLLABORATION_HUB.ID))
                .where(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull());

        // Add name filter if provided
        if (nameFilter != null && !nameFilter.trim().isEmpty()) {
            query = query.and(COLLABORATION_HUB.NAME.containsIgnoreCase(nameFilter.trim()));
        }

        // Add brand filter if provided
        if (brandId != null) {
            query = query.and(COLLABORATION_HUB.BRAND_ID.eq(brandId));
        }

        return query.fetchOne(0, Long.class);
    }

    /**
     * Finds a collaboration hub by ID with access validation.
     * Ensures the user is a participant in the hub.
     *
     * @param hubId the hub ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the user ID to validate access
     * @return the collaboration hub if found and accessible
     */
    public Optional<CollaborationHub> findByIdWithAccess(Long hubId, Long accountId, Long userId) {
        var result = dsl.select(COLLABORATION_HUB.fields())
                .from(COLLABORATION_HUB)
                .join(HUB_PARTICIPANT).on(HUB_PARTICIPANT.HUB_ID.eq(COLLABORATION_HUB.ID))
                .where(COLLABORATION_HUB.ID.eq(hubId))
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOneInto(CollaborationHub.class);

        return Optional.ofNullable(result);
    }

    /**
     * Gets the user's role in a specific hub.
     *
     * @param hubId the hub ID
     * @param userId the user ID
     * @return the user's role in the hub, or null if not a participant
     */
    public HubParticipantRole getUserRoleInHub(Long hubId, Long userId) {
        return dsl.select(HUB_PARTICIPANT.ROLE)
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOneInto(HubParticipantRole.class);
    }

    /**
     * Checks if a hub name already exists for the account.
     *
     * @param name the hub name to check
     * @param accountId the account ID
     * @param excludeHubId optional hub ID to exclude from the check (for updates)
     * @return true if the name exists, false otherwise
     */
    public boolean existsByNameAndAccount(String name, Long accountId, Long excludeHubId) {
        var query = dsl.selectCount()
                .from(COLLABORATION_HUB)
                .where(COLLABORATION_HUB.NAME.equalIgnoreCase(name))
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId));

        if (excludeHubId != null) {
            query = query.and(COLLABORATION_HUB.ID.ne(excludeHubId));
        }

        return query.fetchOne(0, Integer.class) > 0;
    }

    /**
     * Gets the brand name for a hub.
     *
     * @param hubId the hub ID
     * @return the brand name
     */
    public String getBrandNameForHub(Long hubId) {
        return dsl.select(BRAND.NAME)
                .from(COLLABORATION_HUB)
                .join(BRAND).on(BRAND.ID.eq(COLLABORATION_HUB.BRAND_ID))
                .where(COLLABORATION_HUB.ID.eq(hubId))
                .fetchOneInto(String.class);
    }

    /**
     * Gets the account ID for a hub.
     *
     * @param hubId the hub ID
     * @return the account ID
     */
    public Long getAccountIdForHub(Long hubId) {
        return dsl.select(COLLABORATION_HUB.ACCOUNT_ID)
                .from(COLLABORATION_HUB)
                .where(COLLABORATION_HUB.ID.eq(hubId))
                .fetchOneInto(Long.class);
    }

    /**
     * Gets brand names for multiple hubs in bulk to avoid N+1 queries.
     *
     * @param hubIds the list of hub IDs
     * @return map of hub ID to brand name
     */
    public Map<Long, String> getBrandNamesForHubs(List<Long> hubIds) {
        if (hubIds == null || hubIds.isEmpty()) {
            return Map.of();
        }

        return dsl.select(COLLABORATION_HUB.ID, BRAND.NAME)
                .from(COLLABORATION_HUB)
                .join(BRAND).on(BRAND.ID.eq(COLLABORATION_HUB.BRAND_ID))
                .where(COLLABORATION_HUB.ID.in(hubIds))
                .fetchMap(COLLABORATION_HUB.ID, BRAND.NAME);
    }

    /**
     * Gets user roles for multiple hubs in bulk to avoid N+1 queries.
     *
     * @param hubIds the list of hub IDs
     * @param userId the user ID
     * @return map of hub ID to user role
     */
    public Map<Long, HubParticipantRole> getUserRolesInHubs(List<Long> hubIds, Long userId) {
        if (hubIds == null || hubIds.isEmpty()) {
            return Map.of();
        }

        return dsl.select(HUB_PARTICIPANT.HUB_ID, HUB_PARTICIPANT.ROLE)
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.in(hubIds))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchMap(HUB_PARTICIPANT.HUB_ID, HUB_PARTICIPANT.ROLE);
    }
}
