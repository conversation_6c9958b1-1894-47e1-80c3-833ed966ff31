package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.ReviewStatus;

import java.time.LocalDateTime;

/**
 * Response DTO for individual post review details.
 */
public class PostReviewResponse {

    private Long id;

    @JsonProperty("post_id")
    private Long postId;

    @JsonProperty("reviewer_id")
    private Long reviewerId;

    @JsonProperty("reviewer_name")
    private String reviewerName;

    @JsonProperty("reviewer_email")
    private String reviewerEmail;

    @JsonProperty("is_external")
    private Boolean isExternal;

    private ReviewStatus decision;

    private String comment;

    @JsonProperty("reviewed_at")
    private LocalDateTime reviewedAt;

    @JsonProperty("assigned_at")
    private LocalDateTime assignedAt;

    public PostReviewResponse() {}

    public PostReviewResponse(Long id, Long postId, Long reviewerId, String reviewerName, 
                             String reviewerEmail, Boolean isExternal, ReviewStatus decision, 
                             String comment, LocalDateTime reviewedAt, LocalDateTime assignedAt) {
        this.id = id;
        this.postId = postId;
        this.reviewerId = reviewerId;
        this.reviewerName = reviewerName;
        this.reviewerEmail = reviewerEmail;
        this.isExternal = isExternal;
        this.decision = decision;
        this.comment = comment;
        this.reviewedAt = reviewedAt;
        this.assignedAt = assignedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getReviewerId() {
        return reviewerId;
    }

    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }

    public String getReviewerName() {
        return reviewerName;
    }

    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }

    public String getReviewerEmail() {
        return reviewerEmail;
    }

    public void setReviewerEmail(String reviewerEmail) {
        this.reviewerEmail = reviewerEmail;
    }

    public Boolean getIsExternal() {
        return isExternal;
    }

    public void setIsExternal(Boolean isExternal) {
        this.isExternal = isExternal;
    }

    public ReviewStatus getDecision() {
        return decision;
    }

    public void setDecision(ReviewStatus decision) {
        this.decision = decision;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public LocalDateTime getReviewedAt() {
        return reviewedAt;
    }

    public void setReviewedAt(LocalDateTime reviewedAt) {
        this.reviewedAt = reviewedAt;
    }

    public LocalDateTime getAssignedAt() {
        return assignedAt;
    }

    public void setAssignedAt(LocalDateTime assignedAt) {
        this.assignedAt = assignedAt;
    }

    @Override
    public String toString() {
        return "PostReviewResponse{" +
                "id=" + id +
                ", postId=" + postId +
                ", reviewerId=" + reviewerId +
                ", reviewerName='" + reviewerName + '\'' +
                ", reviewerEmail='" + reviewerEmail + '\'' +
                ", isExternal=" + isExternal +
                ", decision=" + decision +
                ", comment='" + comment + '\'' +
                ", reviewedAt=" + reviewedAt +
                ", assignedAt=" + assignedAt +
                '}';
    }
}
