package com.collabhub.be.modules.collaborationhub.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.HubParticipantRole;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for participant invitation operations.
 * Contains the list of successfully invited participants with their details.
 */
public class HubParticipantInviteResponse {

    @NotNull
    private List<InvitedParticipant> invited;

    public HubParticipantInviteResponse() {}

    public HubParticipantInviteResponse(List<InvitedParticipant> invited) {
        this.invited = invited;
    }

    public List<InvitedParticipant> getInvited() {
        return invited;
    }

    public void setInvited(List<InvitedParticipant> invited) {
        this.invited = invited;
    }

    /**
     * Nested class representing an invited participant in the response.
     */
    public static class InvitedParticipant {
        @NotNull
        private Long id;

        @JsonProperty("user_id")
        private Long userId;

        private String email;
        private String name;

        @NotNull
        private HubParticipantRole role;

        @JsonProperty("is_external")
        @NotNull
        private Boolean isExternal;

        @JsonProperty("magic_link_token")
        private String magicLinkToken;

        @JsonProperty("magic_link_expiry")
        private LocalDateTime magicLinkExpiry;

        @JsonProperty("invited_at")
        @NotNull
        private LocalDateTime invitedAt;

        @JsonProperty("joined_at")
        private LocalDateTime joinedAt;

        @NotNull
        private String status; // "active", "pending"

        public InvitedParticipant() {}

        public InvitedParticipant(Long id, Long userId, String email, String name, 
                                HubParticipantRole role, Boolean isExternal, 
                                String magicLinkToken, LocalDateTime magicLinkExpiry,
                                LocalDateTime invitedAt, LocalDateTime joinedAt, String status) {
            this.id = id;
            this.userId = userId;
            this.email = email;
            this.name = name;
            this.role = role;
            this.isExternal = isExternal;
            this.magicLinkToken = magicLinkToken;
            this.magicLinkExpiry = magicLinkExpiry;
            this.invitedAt = invitedAt;
            this.joinedAt = joinedAt;
            this.status = status;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public HubParticipantRole getRole() { return role; }
        public void setRole(HubParticipantRole role) { this.role = role; }

        public Boolean getIsExternal() { return isExternal; }
        public void setIsExternal(Boolean isExternal) { this.isExternal = isExternal; }

        public String getMagicLinkToken() { return magicLinkToken; }
        public void setMagicLinkToken(String magicLinkToken) { this.magicLinkToken = magicLinkToken; }

        public LocalDateTime getMagicLinkExpiry() { return magicLinkExpiry; }
        public void setMagicLinkExpiry(LocalDateTime magicLinkExpiry) { this.magicLinkExpiry = magicLinkExpiry; }

        public LocalDateTime getInvitedAt() { return invitedAt; }
        public void setInvitedAt(LocalDateTime invitedAt) { this.invitedAt = invitedAt; }

        public LocalDateTime getJoinedAt() { return joinedAt; }
        public void setJoinedAt(LocalDateTime joinedAt) { this.joinedAt = joinedAt; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }

    @Override
    public String toString() {
        return "HubParticipantInviteResponse{" +
                "invited=" + invited +
                '}';
    }
}
