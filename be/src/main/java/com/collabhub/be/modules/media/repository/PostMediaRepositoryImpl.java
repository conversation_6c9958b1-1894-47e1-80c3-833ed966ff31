package com.collabhub.be.modules.media.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.PostMediaDao;
import org.jooq.generated.tables.pojos.PostMedia;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static org.jooq.generated.Tables.*;
import org.jooq.impl.DSL;

/**
 * Repository for PostMedia junction table using jOOQ for database operations.
 * Manages the many-to-many relationship between posts and media files.
 */
@Repository
public class PostMediaRepositoryImpl extends PostMediaDao {

    private final DSLContext dsl;

    public PostMediaRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Creates media associations for a post with proper ordering.
     */
    public void createPostMediaAssociations(Long postId, List<Long> mediaIds) {
        if (mediaIds == null || mediaIds.isEmpty()) {
            return;
        }

        // Delete existing associations first
        deleteByPostId(postId);

        // Create new associations with display order
        for (int i = 0; i < mediaIds.size(); i++) {
            PostMedia postMedia = new PostMedia();
            postMedia.setPostId(postId);
            postMedia.setMediaId(mediaIds.get(i));
            postMedia.setDisplayOrder(i);
            postMedia.setCreatedAt(LocalDateTime.now());
            
            insert(postMedia);
        }
    }

    /**
     * Updates media associations for a post, maintaining order.
     */
    public void updatePostMediaAssociations(Long postId, List<Long> mediaIds) {
        createPostMediaAssociations(postId, mediaIds);
    }

    /**
     * Finds all media IDs associated with a post in display order.
     */
    public List<Long> findMediaIdsByPostId(Long postId) {
        return dsl.select(POST_MEDIA.MEDIA_ID)
                .from(POST_MEDIA)
                .where(POST_MEDIA.POST_ID.eq(postId))
                .orderBy(POST_MEDIA.DISPLAY_ORDER.asc())
                .fetchInto(Long.class);
    }

    /**
     * Finds all post-media associations for a specific post.
     */
    public List<PostMedia> findByPostId(Long postId) {
        return dsl.selectFrom(POST_MEDIA)
                .where(POST_MEDIA.POST_ID.eq(postId))
                .orderBy(POST_MEDIA.DISPLAY_ORDER.asc())
                .fetchInto(PostMedia.class);
    }

    /**
     * Deletes all media associations for a post.
     */
    public void deleteByPostId(Long postId) {
        dsl.deleteFrom(POST_MEDIA)
                .where(POST_MEDIA.POST_ID.eq(postId))
                .execute();
    }

    /**
     * Deletes a specific media association.
     */
    public boolean deletePostMediaAssociation(Long postId, Long mediaId) {
        int deleted = dsl.deleteFrom(POST_MEDIA)
                .where(POST_MEDIA.POST_ID.eq(postId))
                .and(POST_MEDIA.MEDIA_ID.eq(mediaId))
                .execute();

        return deleted > 0;
    }

    /**
     * Checks if a media file is associated with any posts.
     */
    public boolean isMediaUsedInPosts(Long mediaId) {
        return dsl.selectCount()
                .from(POST_MEDIA)
                .where(POST_MEDIA.MEDIA_ID.eq(mediaId))
                .fetchOne(0, Integer.class) > 0;
    }

    /**
     * Finds all posts that use a specific media file.
     */
    public List<Long> findPostIdsByMediaId(Long mediaId) {
        return dsl.select(POST_MEDIA.POST_ID)
                .from(POST_MEDIA)
                .where(POST_MEDIA.MEDIA_ID.eq(mediaId))
                .fetchInto(Long.class);
    }

    /**
     * Gets the maximum display order for a post (for adding new media).
     */
    public int getMaxDisplayOrderForPost(Long postId) {
        Integer maxOrder = dsl.select(DSL.max(POST_MEDIA.DISPLAY_ORDER))
                .from(POST_MEDIA)
                .where(POST_MEDIA.POST_ID.eq(postId))
                .fetchOne(0, Integer.class);

        return maxOrder != null ? maxOrder : -1;
    }

    /**
     * Adds a single media file to a post with the next display order.
     */
    public void addMediaToPost(Long postId, Long mediaId) {
        int nextOrder = getMaxDisplayOrderForPost(postId) + 1;
        
        PostMedia postMedia = new PostMedia();
        postMedia.setPostId(postId);
        postMedia.setMediaId(mediaId);
        postMedia.setDisplayOrder(nextOrder);
        postMedia.setCreatedAt(LocalDateTime.now());
        
        insert(postMedia);
    }
}
