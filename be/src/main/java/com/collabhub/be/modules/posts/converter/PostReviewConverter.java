package com.collabhub.be.modules.posts.converter;

import com.collabhub.be.modules.posts.dto.PostReviewCreateRequest;
import com.collabhub.be.modules.posts.dto.PostReviewResponse;
import com.collabhub.be.modules.posts.dto.PostReviewListResponse;
import org.jooq.generated.tables.pojos.PostReviewer;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.enums.ReviewStatus;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Converter for PostReview entities and DTOs.
 * Handles conversion between database entities and API DTOs.
 */
@Component
public class PostReviewConverter {

    /**
     * Converts a create request DTO to a jOOQ POJO for database operations.
     * Used for both create and update operations (upsert behavior).
     */
    public PostReviewer toPostReviewer(PostReviewCreateRequest request, Long postId, Long participantId) {
        if (request == null) {
            return null;
        }

        PostReviewer postReviewer = new PostReviewer();
        postReviewer.setPostId(postId);
        postReviewer.setParticipantId(participantId);
        postReviewer.setReviewStatus(request.getDecision());
        postReviewer.setReviewNotes(request.getComment());
        postReviewer.setReviewedAt(LocalDateTime.now());

        return postReviewer;
    }

    /**
     * Converts a PostReviewer entity to a response DTO.
     * Includes participant information for complete review details.
     */
    public PostReviewResponse toPostReviewResponse(PostReviewer postReviewer, HubParticipant participant) {
        if (postReviewer == null || participant == null) {
            return null;
        }

        return new PostReviewResponse(
                postReviewer.getId(),
                postReviewer.getPostId(),
                participant.getId(),
                getParticipantDisplayName(participant),
                participant.getEmail(),
                participant.getIsExternal(),
                postReviewer.getReviewStatus(),
                postReviewer.getReviewNotes(),
                postReviewer.getReviewedAt(),
                postReviewer.getAssignedAt()
        );
    }

    /**
     * Converts a list of PostReviewer entities to a list response DTO.
     * Includes review summary statistics.
     */
    public PostReviewListResponse toPostReviewListResponse(Long postId, List<PostReviewResponse> reviews) {
        if (reviews == null) {
            return new PostReviewListResponse(postId, List.of(), createEmptyReviewSummary());
        }

        PostReviewListResponse.ReviewSummary summary = createReviewSummary(reviews);
        return new PostReviewListResponse(postId, reviews, summary);
    }

    /**
     * Creates review summary statistics from a list of reviews.
     */
    private PostReviewListResponse.ReviewSummary createReviewSummary(List<PostReviewResponse> reviews) {
        int totalReviewers = reviews.size();
        int pendingReviews = 0;
        int approvedReviews = 0;
        int reworkReviews = 0;

        for (PostReviewResponse review : reviews) {
            switch (review.getDecision()) {
                case pending -> pendingReviews++;
                case approved -> approvedReviews++;
                case rework -> reworkReviews++;
            }
        }

        return new PostReviewListResponse.ReviewSummary(
                totalReviewers, pendingReviews, approvedReviews, reworkReviews
        );
    }

    /**
     * Creates an empty review summary for posts with no reviews.
     */
    private PostReviewListResponse.ReviewSummary createEmptyReviewSummary() {
        return new PostReviewListResponse.ReviewSummary(0, 0, 0, 0);
    }

    /**
     * Gets the display name for a participant.
     * Prefers the name field, falls back to email if name is not available.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        if (participant.getName() != null && !participant.getName().trim().isEmpty()) {
            return participant.getName();
        }
        return participant.getEmail();
    }

    /**
     * Updates an existing PostReviewer entity with new review data.
     * Used for upsert operations when a review already exists.
     */
    public void updatePostReviewer(PostReviewer existingReviewer, PostReviewCreateRequest request) {
        if (existingReviewer == null || request == null) {
            return;
        }

        existingReviewer.setReviewStatus(request.getDecision());
        existingReviewer.setReviewNotes(request.getComment());
        existingReviewer.setReviewedAt(LocalDateTime.now());
    }

    /**
     * Calculates the overall post status based on all reviewer decisions.
     * Business logic: 
     * - If any reviewer marks "rework": post status = "rework"
     * - If all assigned reviewers approve: post status = "approved"  
     * - Otherwise: status remains "pending"
     */
    public ReviewStatus calculatePostStatus(List<PostReviewResponse> reviews) {
        if (reviews == null || reviews.isEmpty()) {
            return ReviewStatus.pending;
        }

        boolean hasRework = false;
        boolean allReviewed = true;
        boolean allApproved = true;

        for (PostReviewResponse review : reviews) {
            if (review.getDecision() == ReviewStatus.rework) {
                hasRework = true;
            }
            if (review.getDecision() == ReviewStatus.pending) {
                allReviewed = false;
                allApproved = false;
            }
            if (review.getDecision() != ReviewStatus.approved) {
                allApproved = false;
            }
        }

        // If any reviewer requests rework, post needs rework
        if (hasRework) {
            return ReviewStatus.rework;
        }

        // If all reviewers have approved, post is approved
        if (allReviewed && allApproved) {
            return ReviewStatus.approved;
        }

        // Otherwise, post is still pending
        return ReviewStatus.pending;
    }
}
