package com.collabhub.be.modules.collaborationhub.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.service.HubParticipantService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jooq.generated.enums.HubParticipantRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * REST controller for managing collaboration hub participants.
 * Provides endpoints for inviting, listing, updating, and removing participants.
 */
@RestController
@RequestMapping("/api/hubs/{hubId}/participants")
@Tag(name = "Hub Participants", description = "Collaboration hub participant management")
@PreAuthorize("hasAuthority('hub-participant:read')")
public class HubParticipantController {

    private static final Logger logger = LoggerFactory.getLogger(HubParticipantController.class);

    private final HubParticipantService participantService;
    private final JwtClaimsService jwtClaimsService;

    public HubParticipantController(HubParticipantService participantService,
                                  JwtClaimsService jwtClaimsService) {
        this.participantService = participantService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Invites participants to a collaboration hub.
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_PARTICIPANT_INVITE.permission)")
    @Operation(summary = "Invite participants to hub",
               description = "Invites internal users, external users, or brand contacts to a collaboration hub")
    public ResponseEntity<HubParticipantInviteResponse> inviteParticipants(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Valid @RequestBody HubParticipantInviteRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Inviting {} participants to hub {} by user {}", 
                   request.getParticipants().size(), hubId, userContext.getUserId());

        HubParticipantInviteResponse response = participantService.inviteParticipants(
                hubId, request, userContext.getAccountId(), userContext.getUserId());

        logger.info("Successfully invited {} participants to hub {}", 
                   response.getInvited().size(), hubId);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Lists participants in a collaboration hub with pagination and filtering.
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_PARTICIPANT_READ.permission)")
    @Operation(summary = "List hub participants",
               description = "Gets a paginated list of hub participants with optional filtering")
    public ResponseEntity<HubParticipantListResponse> getHubParticipants(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Filter by participant role")
            @RequestParam(value = "role", required = false) HubParticipantRole role,
            @Parameter(description = "Page number (0-based)")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)")
            @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving participants for hub {} with filters: role={}",
                    hubId, role);

        // Validate page size
        if (size > 100) {
            size = 100;
        }

        PageRequest pageRequest = new PageRequest(page, size);
        HubParticipantListResponse response = participantService.getHubParticipants(
                hubId, pageRequest, role, userContext.getAccountId(), userContext.getUserId());

        logger.debug("Retrieved {} participants for hub {}",
                    response.getContent().size(), hubId);
        return ResponseEntity.ok(response);
    }

    /**
     * Gets details of a specific participant.
     */
    @GetMapping("/{participantId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_PARTICIPANT_READ.permission)")
    @Operation(summary = "Get participant details",
               description = "Gets detailed information about a specific hub participant")
    public ResponseEntity<HubParticipantResponse> getParticipantDetails(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Participant ID", required = true)
            @PathVariable Long participantId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Retrieving participant {} details for hub {}", participantId, hubId);

        HubParticipantResponse response = participantService.getParticipantDetails(
                hubId, participantId, userContext.getAccountId(), userContext.getUserId());

        logger.debug("Retrieved participant {} details for hub {}", participantId, hubId);
        return ResponseEntity.ok(response);
    }

    /**
     * Updates a participant's role.
     */
    @PutMapping("/{participantId}/role")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_PARTICIPANT_MANAGE.permission)")
    @Operation(summary = "Update participant role",
               description = "Updates the role of a hub participant (admin permission required)")
    public ResponseEntity<HubParticipantResponse> updateParticipantRole(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Participant ID", required = true)
            @PathVariable Long participantId,
            @Valid @RequestBody HubParticipantUpdateRoleRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Updating participant {} role to {} in hub {} by user {}", 
                   participantId, request.getRole(), hubId, userContext.getUserId());

        HubParticipantResponse response = participantService.updateParticipantRole(
                hubId, participantId, request, userContext.getAccountId(), userContext.getUserId());

        logger.info("Successfully updated participant {} role in hub {}", participantId, hubId);
        return ResponseEntity.ok(response);
    }

    /**
     * Removes a participant from the hub.
     */
    @DeleteMapping("/{participantId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_PARTICIPANT_MANAGE.permission)")
    @Operation(summary = "Remove participant",
               description = "Removes a participant from the hub (admin permission required)")
    public ResponseEntity<Void> removeParticipant(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Participant ID", required = true)
            @PathVariable Long participantId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Removing participant {} from hub {} by user {}", 
                   participantId, hubId, userContext.getUserId());

        participantService.removeParticipant(
                hubId, participantId, userContext.getAccountId(), userContext.getUserId());

        logger.info("Successfully removed participant {} from hub {}", participantId, hubId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Resends invitation to an external participant.
     */
    @PostMapping("/{participantId}/resend-invite")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_PARTICIPANT_INVITE.permission)")
    @Operation(summary = "Resend invitation",
               description = "Resends invitation email to an external participant (admin permission required)")
    public ResponseEntity<Void> resendInvitation(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long hubId,
            @Parameter(description = "Participant ID", required = true)
            @PathVariable Long participantId,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Resending invitation to participant {} in hub {} by user {}", 
                   participantId, hubId, userContext.getUserId());

        participantService.resendInvitation(
                hubId, participantId, userContext.getAccountId(), userContext.getUserId());

        logger.info("Successfully resent invitation to participant {} in hub {}", participantId, hubId);
        return ResponseEntity.ok().build();
    }
}
