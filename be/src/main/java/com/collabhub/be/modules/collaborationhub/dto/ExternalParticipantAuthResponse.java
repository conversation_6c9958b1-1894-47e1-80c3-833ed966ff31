package com.collabhub.be.modules.collaborationhub.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.HubParticipantRole;

import jakarta.validation.constraints.NotNull;

/**
 * Response DTO for external participant authentication via magic link.
 * Contains access token and participant information, following the same pattern as regular authentication.
 */
public class ExternalParticipantAuthResponse {

    @JsonProperty("access_token")
    @NotNull
    private String accessToken;

    @JsonProperty("token_type")
    @NotNull
    private String tokenType = "Bearer";

    @JsonProperty("expires_in")
    @NotNull
    private Long expiresIn;

    @NotNull
    private ParticipantInfo participant;

    @NotNull
    private HubInfo hub;

    @JsonProperty("is_first_access")
    private Boolean isFirstAccess;

    public ExternalParticipantAuthResponse() {}

    public ExternalParticipantAuthResponse(String accessToken, Long expiresIn, 
                                         ParticipantInfo participant, HubInfo hub, 
                                         Boolean isFirstAccess) {
        this.accessToken = accessToken;
        this.expiresIn = expiresIn;
        this.participant = participant;
        this.hub = hub;
        this.isFirstAccess = isFirstAccess;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public ParticipantInfo getParticipant() {
        return participant;
    }

    public void setParticipant(ParticipantInfo participant) {
        this.participant = participant;
    }

    public HubInfo getHub() {
        return hub;
    }

    public void setHub(HubInfo hub) {
        this.hub = hub;
    }

    public Boolean getIsFirstAccess() {
        return isFirstAccess;
    }

    public void setIsFirstAccess(Boolean isFirstAccess) {
        this.isFirstAccess = isFirstAccess;
    }

    /**
     * Nested class representing participant information in the response.
     */
    public static class ParticipantInfo {
        @NotNull
        private Long id;

        @NotNull
        private String email;

        @JsonProperty("display_name")
        @NotNull
        private String displayName;

        @NotNull
        private HubParticipantRole role;

        @JsonProperty("is_external")
        @NotNull
        private Boolean isExternal;

        public ParticipantInfo() {}

        public ParticipantInfo(Long id, String email, String displayName, 
                             HubParticipantRole role, Boolean isExternal) {
            this.id = id;
            this.email = email;
            this.displayName = displayName;
            this.role = role;
            this.isExternal = isExternal;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getDisplayName() {
            return displayName;
        }

        public void setDisplayName(String displayName) {
            this.displayName = displayName;
        }

        public HubParticipantRole getRole() {
            return role;
        }

        public void setRole(HubParticipantRole role) {
            this.role = role;
        }

        public Boolean getIsExternal() {
            return isExternal;
        }

        public void setIsExternal(Boolean isExternal) {
            this.isExternal = isExternal;
        }
    }

    /**
     * Nested class representing hub information in the response.
     */
    public static class HubInfo {
        @NotNull
        private Long id;

        @NotNull
        private String name;

        @JsonProperty("account_id")
        @NotNull
        private Long accountId;

        @JsonProperty("brand_id")
        @NotNull
        private Long brandId;

        public HubInfo() {}

        public HubInfo(Long id, String name, Long accountId, Long brandId) {
            this.id = id;
            this.name = name;
            this.accountId = accountId;
            this.brandId = brandId;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getAccountId() {
            return accountId;
        }

        public void setAccountId(Long accountId) {
            this.accountId = accountId;
        }

        public Long getBrandId() {
            return brandId;
        }

        public void setBrandId(Long brandId) {
            this.brandId = brandId;
        }
    }

    @Override
    public String toString() {
        return "ExternalParticipantAuthResponse{" +
                "accessToken='[REDACTED]'" +
                ", tokenType='" + tokenType + '\'' +
                ", expiresIn=" + expiresIn +
                ", participant=" + participant +
                ", hub=" + hub +
                ", isFirstAccess=" + isFirstAccess +
                '}';
    }
}
