package com.collabhub.be.modules.posts.constants;

/**
 * Centralized constants for the posts module.
 * Contains validation limits, default values, and other configuration constants.
 */
public final class PostConstants {

    private PostConstants() {
        // Utility class - prevent instantiation
    }

    // Validation Constants
    public static final int MAX_CAPTION_LENGTH = 2200;
    public static final int MAX_REVIEWER_NOTES_LENGTH = 1000;
    public static final int MAX_REVIEWERS_COUNT = 10;
    public static final int MAX_REVIEW_COMMENT_LENGTH = 2000;
    
    // Validation Messages
    public static final String CAPTION_TOO_LONG_MESSAGE = "Caption cannot exceed " + MAX_CAPTION_LENGTH + " characters";
    public static final String REVIEWER_NOTES_TOO_LONG_MESSAGE = "Reviewer notes cannot exceed " + MAX_REVIEWER_NOTES_LENGTH + " characters";
    public static final String TOO_MANY_REVIEWERS_MESSAGE = "Cannot assign more than " + MAX_REVIEWERS_COUNT + " reviewers to a post";
    public static final String POST_CONTENT_REQUIRED_MESSAGE = "Post must have either caption or media content";
    public static final String REVIEW_COMMENT_TOO_LONG_MESSAGE = "Review comment cannot exceed " + MAX_REVIEW_COMMENT_LENGTH + " characters";
    public static final String REVIEW_COMMENT_REQUIRED_MESSAGE = "Review comment is required";
    public static final String REVIEW_DECISION_REQUIRED_MESSAGE = "Review decision is required";
    
    // Error Messages
    public static final String POST_NOT_FOUND_MESSAGE = "Post not found";
    public static final String ACCESS_DENIED_MESSAGE = "Access denied to post";
    public static final String CANNOT_EDIT_POST_MESSAGE = "Cannot edit this post";
    public static final String CANNOT_DELETE_POST_MESSAGE = "Cannot delete this post";
    public static final String INVALID_MEDIA_FILE_MESSAGE = "Invalid or inaccessible media file";
    public static final String USER_NOT_PARTICIPANT_MESSAGE = "User is not a participant in hub";
    public static final String CANNOT_CREATE_POSTS_MESSAGE = "User role cannot create posts";
    public static final String CANNOT_REVIEW_POST_MESSAGE = "User cannot review this post";
    public static final String NOT_ASSIGNED_REVIEWER_MESSAGE = "User is not assigned as a reviewer for this post";
    public static final String REVIEW_NOT_FOUND_MESSAGE = "Review not found";
    
    // Default Values
    public static final String DEFAULT_FILTER = "all";
}
