package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.constants.JwtClaims;
import com.collabhub.be.modules.auth.service.RefreshTokenService;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import org.jooq.generated.tables.pojos.CollaborationHub;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.RefreshToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.jose.jws.MacAlgorithm;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for authenticating external participants via magic links.
 * Generates JWT tokens for external users to access collaboration hubs.
 */
@Service
public class ExternalParticipantAuthService {

    private static final Logger logger = LoggerFactory.getLogger(ExternalParticipantAuthService.class);

    private final MagicLinkAuthService magicLinkAuthService;
    private final RefreshTokenService refreshTokenService;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationHubRepositoryImpl hubRepository;
    private final JwtEncoder jwtEncoder;
    private final AuthProperties authProperties;

    public ExternalParticipantAuthService(MagicLinkAuthService magicLinkAuthService,
                                        RefreshTokenService refreshTokenService,
                                        HubParticipantRepositoryImpl participantRepository,
                                        CollaborationHubRepositoryImpl hubRepository,
                                        JwtEncoder jwtEncoder,
                                        AuthProperties authProperties) {
        this.magicLinkAuthService = magicLinkAuthService;
        this.refreshTokenService = refreshTokenService;
        this.participantRepository = participantRepository;
        this.hubRepository = hubRepository;
        this.jwtEncoder = jwtEncoder;
        this.authProperties = authProperties;
    }

    /**
     * Authenticates an external participant via magic link and generates tokens.
     *
     * @param token the magic link token
     * @param userAgent the client user agent
     * @return authentication result with tokens and participant info
     */
    @Transactional
    public ExternalAuthenticationResult authenticateWithMagicLink(String token, String userAgent) {
        logger.info("Authenticating external participant with magic link");

        // Validate magic link token
        MagicLinkAuthService.MagicLinkValidationResult validationResult = 
                magicLinkAuthService.validateMagicLinkToken(token);

        // Get participant details
        HubParticipant participant = participantRepository.findByIdAndHubId(
                validationResult.getParticipantId(), validationResult.getHubId())
                .orElseThrow(() -> new RuntimeException("Participant not found"));

        // Get hub details for account context
        CollaborationHub hub = hubRepository.findById(validationResult.getHubId());
        if (hub == null) {
            throw new RuntimeException("Hub not found");
        }

        // Generate JWT access token for external participant
        String accessToken = generateExternalParticipantAccessToken(participant, hub);

        // Create refresh token using participant ID as user ID
        // External participants use negative IDs to distinguish from regular users
        Long externalUserId = -validationResult.getParticipantId();
        RefreshToken refreshToken = refreshTokenService.createRefreshToken(externalUserId, userAgent);

        logger.info("Successfully authenticated external participant {} for hub {}", 
                   validationResult.getParticipantId(), validationResult.getHubId());

        return new ExternalAuthenticationResult(
                accessToken,
                refreshToken.getToken(),
                participant,
                hub,
                validationResult.isFirstAccess()
        );
    }

    /**
     * Refreshes tokens for an external participant.
     *
     * @param refreshTokenString the refresh token
     * @param userAgent the client user agent
     * @return new authentication result with refreshed tokens
     */
    @Transactional
    public ExternalAuthenticationResult refreshExternalParticipantToken(String refreshTokenString, String userAgent) {
        logger.debug("Refreshing external participant token");

        // Validate refresh token
        Optional<RefreshToken> refreshTokenOpt = refreshTokenService.validateRefreshToken(refreshTokenString);
        if (refreshTokenOpt.isEmpty()) {
            throw new RuntimeException("Invalid or expired refresh token");
        }

        RefreshToken refreshToken = refreshTokenOpt.get();
        
        // Extract participant ID from external user ID (negative value)
        Long participantId = -refreshToken.getUserId();
        
        // Find participant
        HubParticipant participant = participantRepository.fetchOneById(participantId);
        if (participant == null || participant.getRemovedAt() != null) {
            refreshTokenService.revokeRefreshToken(refreshToken);
            throw new RuntimeException("Participant not found or removed");
        }

        // Get hub details
        CollaborationHub hub = hubRepository.findById(participant.getHubId());
        if (hub == null) {
            throw new RuntimeException("Hub not found");
        }

        // Generate new access token
        String accessToken = generateExternalParticipantAccessToken(participant, hub);

        // Rotate refresh token
        RefreshToken newRefreshToken = refreshTokenService.rotateRefreshToken(refreshToken, userAgent);

        logger.info("Successfully refreshed token for external participant {}", participantId);

        return new ExternalAuthenticationResult(
                accessToken,
                newRefreshToken.getToken(),
                participant,
                hub,
                false // Not first access for refresh
        );
    }

    /**
     * Generates a JWT access token for an external participant.
     */
    private String generateExternalParticipantAccessToken(HubParticipant participant, CollaborationHub hub) {
        Instant now = Instant.now();
        Instant expiry = now.plus(authProperties.getJwt().getAccessTokenTtl());

        // Use participant ID as subject for external users
        String subject = "ext_" + participant.getId();

        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer(authProperties.getJwt().getIssuer())
                .audience(Collections.singletonList(authProperties.getJwt().getAudience()))
                .subject(subject)
                .issuedAt(now)
                .expiresAt(expiry)
                .notBefore(now)
                .id(UUID.randomUUID().toString()) // jti claim
                .claim(JwtClaims.EMAIL, participant.getEmail())
                .claim(JwtClaims.DISPLAY_NAME, extractDisplayName(participant))
                .claim(JwtClaims.ROLE, participant.getRole().name())
                .claim(JwtClaims.ACCOUNT_ID, hub.getAccountId())
                .claim(JwtClaims.INTERNAL, false) // External participant
                // Additional claims for external participants
                .claim("participant_id", participant.getId())
                .claim("hub_id", participant.getHubId())
                .claim("participant_role", participant.getRole().name())
                .claim("is_external", true)
                .build();

        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    /**
     * Extracts display name from participant email.
     */
    private String extractDisplayName(HubParticipant participant) {
        String email = participant.getEmail();
        if (email != null && email.contains("@")) {
            return email.substring(0, email.indexOf('@'));
        }
        return email;
    }

    /**
     * Result of external participant authentication.
     */
    public static class ExternalAuthenticationResult {
        private final String accessToken;
        private final String refreshToken;
        private final HubParticipant participant;
        private final CollaborationHub hub;
        private final boolean isFirstAccess;

        public ExternalAuthenticationResult(String accessToken, String refreshToken, 
                                          HubParticipant participant, CollaborationHub hub, 
                                          boolean isFirstAccess) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.participant = participant;
            this.hub = hub;
            this.isFirstAccess = isFirstAccess;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public HubParticipant getParticipant() {
            return participant;
        }

        public CollaborationHub getHub() {
            return hub;
        }

        public boolean isFirstAccess() {
            return isFirstAccess;
        }
    }
}
