package com.collabhub.be.modules.invoice.service;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.InternalServerErrorException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.invoice.constants.InvoiceConstants;
import com.collabhub.be.modules.bankdetails.repository.BankDetailsRepositoryImpl;
import com.collabhub.be.modules.brands.repository.BrandRepositoryImpl;
import com.collabhub.be.modules.companies.repository.AccountCompanyRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.InvoiceResponse;
import com.collabhub.be.modules.invoice.repository.InvoiceDeliveryLogRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.InvoiceSnapshotResponse;
import com.collabhub.be.modules.invoice.repository.InvoiceRecipientRepositoryImpl;
import com.collabhub.be.modules.invoice.repository.InvoiceRepositoryImpl;
import org.jooq.generated.enums.InvoiceStatus;
import org.jooq.generated.enums.RecipientType;
import org.jooq.generated.tables.pojos.Invoice;
import org.jooq.generated.tables.pojos.InvoiceDeliveryLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.util.ByteArrayDataSource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Service for sending invoice emails with PDF attachments.
 * Handles delivery tracking, status updates, and retry mechanisms.
 */
@Service
public class InvoiceEmailService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceEmailService.class);
    
    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    private final InvoicePdfService pdfService;
    private final InvoiceRepositoryImpl invoiceRepository;
    private final InvoiceRecipientRepositoryImpl recipientRepository;
    private final InvoiceDeliveryLogRepositoryImpl deliveryLogRepository;
    private final InvoiceSnapshotService snapshotService;
    private final AccountCompanyRepositoryImpl accountCompanyRepository;
    private final BrandRepositoryImpl brandRepository;
    private final BankDetailsRepositoryImpl bankDetailsRepository;
    private final InvoiceValidationService validationService;
    private final String frontendUrl;
    private final Executor emailExecutor;

    public InvoiceEmailService(JavaMailSender mailSender,
                              TemplateEngine templateEngine,
                              InvoicePdfService pdfService,
                              InvoiceRepositoryImpl invoiceRepository,
                              InvoiceRecipientRepositoryImpl recipientRepository,
                              InvoiceDeliveryLogRepositoryImpl deliveryLogRepository,
                              InvoiceSnapshotService snapshotService,
                              AccountCompanyRepositoryImpl accountCompanyRepository,
                              BrandRepositoryImpl brandRepository,
                              BankDetailsRepositoryImpl bankDetailsRepository,
                              InvoiceValidationService validationService,
                              @Value("${app.frontend-url}") String frontendUrl) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
        this.pdfService = pdfService;
        this.invoiceRepository = invoiceRepository;
        this.recipientRepository = recipientRepository;
        this.deliveryLogRepository = deliveryLogRepository;
        this.snapshotService = snapshotService;
        this.accountCompanyRepository = accountCompanyRepository;
        this.brandRepository = brandRepository;
        this.bankDetailsRepository = bankDetailsRepository;
        this.validationService = validationService;
        this.frontendUrl = frontendUrl;
        this.emailExecutor = Executors.newFixedThreadPool(InvoiceConstants.EMAIL_THREAD_POOL_SIZE); // Pool for async email sending
    }

    /**
     * Sends invoice email to all recipients.
     *
     * @param invoice the invoice to send
     * @param accountId the account ID for multi-tenancy
     * @return true if all emails were sent successfully
     */
    @Transactional
    public boolean sendInvoiceEmail(InvoiceResponse invoice, Long accountId) {
        return sendInvoiceEmail(invoice, accountId, false);
    }

    /**
     * Sends invoice email to all recipients with option to force send.
     *
     * @param invoice the invoice to send
     * @param accountId the account ID for multi-tenancy
     * @param forceSend if true, bypasses duplicate sending prevention
     * @return true if all emails were sent successfully
     * @throws BadRequestException if invoice has already been sent and forceSend is false
     */
    @Transactional
    public boolean sendInvoiceEmail(InvoiceResponse invoice, Long accountId, boolean forceSend) {
        logger.info("Sending invoice email for invoice {} to {} recipients (force: {})",
                   invoice.getInvoiceNumber(), invoice.getRecipients().size(), forceSend);

        // Validate sending prerequisites
        validateInvoiceForSending(invoice, accountId, forceSend);

        // Generate PDF for attachment
        byte[] pdfBytes = generatePdfForEmail(invoice);

        // Send to all recipients
        boolean allSent = sendToAllRecipients(invoice, pdfBytes);

        // Update invoice status if any emails were successful
        updateInvoiceStatusIfNeeded(invoice.getId(), accountId);

        return allSent;
    }

    /**
     * Validates that the invoice can be sent.
     */
    private void validateInvoiceForSending(InvoiceResponse invoice, Long accountId, boolean forceSend) {
        // Check if invoice has already been sent successfully (unless forcing)
        if (!forceSend && hasBeenSentSuccessfully(invoice.getId())) {
            logger.warn("Attempted to send already-sent invoice {} without force flag", invoice.getInvoiceNumber());
            throw new BadRequestException(ErrorCode.DUPLICATE_OPERATION,
                    InvoiceConstants.INVOICE_ALREADY_SENT_MESSAGE);
        }

        // Validate that invoice has required issuer and recipient data before sending
        Invoice invoiceEntity = invoiceRepository.findByIdAndAccountId(invoice.getId(), accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, InvoiceConstants.INVOICE_NOT_FOUND_MESSAGE));
        validationService.validateInvoiceForSendingOrPdf(invoiceEntity);
    }

    /**
     * Generates PDF for email attachment.
     */
    private byte[] generatePdfForEmail(InvoiceResponse invoice) {
        try {
            return pdfService.generateInvoicePdf(invoice);
        } catch (Exception e) {
            logger.error("Failed to generate PDF for invoice {}: {}", invoice.getInvoiceNumber(), e.getMessage());
            LocalDateTime sentAt = LocalDateTime.now();
            logDeliveryFailure(invoice.getId(), "PDF_GENERATION_FAILED",
                             "Failed to generate PDF: " + e.getMessage(), sentAt);
            throw new InternalServerErrorException(ErrorCode.PDF_GENERATION_ERROR,
                    "Failed to generate invoice PDF: " + e.getMessage());
        }
    }

    /**
     * Sends email to all recipients and tracks results.
     */
    private boolean sendToAllRecipients(InvoiceResponse invoice, byte[] pdfBytes) {
        boolean allSent = true;
        LocalDateTime sentAt = LocalDateTime.now();

        for (var recipient : invoice.getRecipients()) {
            try {
                boolean sent = sendToRecipient(invoice, recipient.getEmail(), recipient.getType(), pdfBytes);

                if (sent) {
                    handleSuccessfulDelivery(invoice, recipient.getEmail(), sentAt);
                } else {
                    allSent = false;
                    logger.warn("Failed to send invoice {} to {}", invoice.getInvoiceNumber(), recipient.getEmail());
                }

            } catch (Exception e) {
                allSent = false;
                handleFailedDelivery(invoice, recipient.getEmail(), e, sentAt);
            }
        }

        return allSent;
    }

    /**
     * Handles successful email delivery.
     */
    private void handleSuccessfulDelivery(InvoiceResponse invoice, String email, LocalDateTime sentAt) {
        // Update recipient tracking
        recipientRepository.updateSendTracking(invoice.getId(), email, sentAt);

        // Log successful delivery
        logDeliverySuccess(invoice.getId(), email, sentAt);

        logger.debug("Successfully sent invoice {} to {}", invoice.getInvoiceNumber(), email);
    }

    /**
     * Handles failed email delivery.
     */
    private void handleFailedDelivery(InvoiceResponse invoice, String email, Exception e, LocalDateTime sentAt) {
        logger.error("Error sending invoice {} to {}: {}",
                   invoice.getInvoiceNumber(), email, e.getMessage(), e);

        logDeliveryFailure(invoice.getId(), email, "SEND_ERROR", e.getMessage(), sentAt);
    }

    /**
     * Updates invoice status to sent if needed.
     */
    private void updateInvoiceStatusIfNeeded(Long invoiceId, Long accountId) {
        if (hasSuccessfulDeliveries(invoiceId)) {
            updateInvoiceStatusToSent(invoiceId, accountId);
        }
    }

    /**
     * Sends invoice email asynchronously.
     *
     * @param invoice the invoice to send
     * @param accountId the account ID for multi-tenancy
     * @return CompletableFuture with result
     */
    public CompletableFuture<Boolean> sendInvoiceEmailAsync(InvoiceResponse invoice, Long accountId) {
        return CompletableFuture.supplyAsync(() -> sendInvoiceEmail(invoice, accountId), emailExecutor);
    }

    /**
     * Sends invoice to a specific recipient.
     *
     * @param invoice the invoice data
     * @param recipientEmail the recipient email
     * @param recipientType the recipient type (original/copy)
     * @param pdfBytes the PDF attachment
     * @return true if sent successfully
     */
    private boolean sendToRecipient(InvoiceResponse invoice, String recipientEmail, 
                                   RecipientType recipientType, byte[] pdfBytes) {
        try {
            // Create email subject
            String subject = createEmailSubject(invoice, recipientType);
            
            // Generate HTML content
            String htmlContent = generateEmailContent(invoice, recipientType);
            
            // Create and send email
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            
            helper.setTo(recipientEmail);
            helper.setSubject(subject);
            helper.setFrom("<EMAIL>");
            helper.setText(htmlContent, true);
            
            // Attach PDF
            String filename = "Invoice_" + invoice.getInvoiceNumber() + ".pdf";
            helper.addAttachment(filename, new ByteArrayDataSource(pdfBytes, "application/pdf"));
            
            mailSender.send(mimeMessage);
            return true;
            
        } catch (MailException | MessagingException e) {
            logger.error("Failed to send invoice email to {}: {}", recipientEmail, e.getMessage());
            return false;
        }
    }

    /**
     * Creates email subject based on invoice and recipient type.
     */
    private String createEmailSubject(InvoiceResponse invoice, RecipientType recipientType) {
        String prefix = recipientType == RecipientType.copy ? "[COPY] " : "";
        String statusSuffix = invoice.getStatus() == InvoiceStatus.overdue ? " - OVERDUE" : "";
        
        return String.format("%sInvoice %s%s", prefix, invoice.getInvoiceNumber(), statusSuffix);
    }

    /**
     * Generates email HTML content using Thymeleaf template.
     */
    private String generateEmailContent(InvoiceResponse invoice, RecipientType recipientType) {
        Context context = new Context();

        // Add invoice data
        context.setVariable("invoice", invoice);
        context.setVariable("recipientType", recipientType);
        context.setVariable("isCopy", recipientType == RecipientType.copy);
        context.setVariable("frontendUrl", frontendUrl);

        // Add snapshot data for email templates (fetches live data for DRAFT, snapshots for non-DRAFT)
        context.setVariable("issuer", fetchIssuerSnapshot(invoice));
        context.setVariable("recipient", fetchRecipientSnapshot(invoice));
        context.setVariable("bankDetails", fetchBankDetailsSnapshot(invoice));
        
        // Add formatted amounts
        context.setVariable("formattedTotalAmount", 
                           formatCurrency(invoice.getTotalAmount(), invoice.getCurrency()));
        
        // Add status information
        context.setVariable("isOverdue", invoice.getIsOverdue());
        context.setVariable("daysUntilDue", invoice.getDaysUntilDue());
        
        // Process template
        return templateEngine.process("email/invoice-delivery", context);
    }

    /**
     * Logs successful delivery.
     */
    private void logDeliverySuccess(Long invoiceId, String recipientEmail, LocalDateTime sentAt) {
        InvoiceDeliveryLog log = new InvoiceDeliveryLog();
        log.setInvoiceId(invoiceId);
        log.setRecipientEmail(recipientEmail);
        log.setDeliveryStatus("sent");
        log.setMessageContent("Email sent successfully");
        log.setCreatedAt(sentAt);

        deliveryLogRepository.insert(log);
    }

    /**
     * Logs delivery failure.
     */
    private void logDeliveryFailure(Long invoiceId, String recipientEmail, String errorType,
                                   String errorMessage, LocalDateTime attemptedAt) {
        InvoiceDeliveryLog log = new InvoiceDeliveryLog();
        log.setInvoiceId(invoiceId);
        log.setRecipientEmail(recipientEmail);
        log.setDeliveryStatus("failed");
        log.setErrorMessage(errorType + ": " + errorMessage);
        log.setCreatedAt(attemptedAt);

        deliveryLogRepository.insert(log);
    }

    /**
     * Logs delivery failure without recipient (e.g., PDF generation failure).
     */
    private void logDeliveryFailure(Long invoiceId, String errorType, String errorMessage, LocalDateTime attemptedAt) {
        logDeliveryFailure(invoiceId, null, errorType, errorMessage, attemptedAt);
    }

    /**
     * Checks if there are any successful deliveries for an invoice.
     */
    private boolean hasSuccessfulDeliveries(Long invoiceId) {
        return deliveryLogRepository.countSuccessfulDeliveries(invoiceId) > 0;
    }

    /**
     * Updates invoice status to 'sent'.
     */
    private void updateInvoiceStatusToSent(Long invoiceId, Long accountId) {
        try {
            boolean updated = invoiceRepository.updateStatus(invoiceId, accountId, InvoiceStatus.sent);
            if (updated) {
                logger.info("Updated invoice {} status to 'sent'", invoiceId);
            } else {
                logger.warn("Failed to update invoice {} status to 'sent'", invoiceId);
            }
        } catch (Exception e) {
            logger.error("Error updating invoice {} status to 'sent': {}", invoiceId, e.getMessage());
        }
    }

    /**
     * Simple currency formatting.
     */
    private String formatCurrency(java.math.BigDecimal amount, String currency) {
        if (amount == null) return "0.00";
        
        String formatted = amount.setScale(2, java.math.RoundingMode.HALF_UP).toString();
        return switch (currency != null ? currency.toUpperCase() : "EUR") {
            case "USD" -> "$" + formatted;
            case "GBP" -> "£" + formatted;
            case "EUR" -> "€" + formatted;
            default -> formatted + " " + currency;
        };
    }

    /**
     * Retries failed deliveries for an invoice.
     *
     * @param invoiceId the invoice ID
     * @param accountId the account ID
     * @return true if retry was successful
     */
    @Transactional
    public boolean retryFailedDeliveries(Long invoiceId, Long accountId) {
        logger.info("Retrying failed deliveries for invoice {}", invoiceId);
        
        // Get failed deliveries
        List<InvoiceDeliveryLog> failedDeliveries = deliveryLogRepository.findFailedDeliveries(invoiceId);
        
        if (failedDeliveries.isEmpty()) {
            logger.debug("No failed deliveries found for invoice {}", invoiceId);
            return true;
        }

        // This would require getting the full invoice data again
        // Implementation would be similar to sendInvoiceEmail but only for failed recipients
        logger.info("Found {} failed deliveries for invoice {} - retry implementation needed", 
                   failedDeliveries.size(), invoiceId);
        
        return false; // Placeholder - full implementation would retry specific recipients
    }

    /**
     * Fetches issuer snapshot for email generation.
     * For DRAFT invoices, fetches live data from AccountCompany.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse fetchIssuerSnapshot(InvoiceResponse invoice) {
        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            // For DRAFT invoices, fetch live data
            if (invoice.getIssuerId() == null) {
                return new InvoiceSnapshotResponse.IssuerSnapshotResponse();
            }

            return accountCompanyRepository.findByIdAndAccountId(invoice.getIssuerId(), invoice.getAccountId())
                    .map(this::convertAccountCompanyToIssuerResponse)
                    .orElse(new InvoiceSnapshotResponse.IssuerSnapshotResponse());
        } else {
            // For non-DRAFT invoices, fetch snapshot data
            return snapshotService.getIssuerSnapshotByHash(invoice.getIssuerSnapshotHash());
        }
    }

    /**
     * Fetches recipient snapshot for email generation.
     * For DRAFT invoices, fetches live data from Brand.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse fetchRecipientSnapshot(InvoiceResponse invoice) {
        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            // For DRAFT invoices, fetch live data
            if (invoice.getRecipientId() == null) {
                return new InvoiceSnapshotResponse.RecipientSnapshotResponse();
            }

            return brandRepository.findByIdAndAccountId(invoice.getRecipientId(), invoice.getAccountId())
                    .map(this::convertBrandToRecipientResponse)
                    .orElse(new InvoiceSnapshotResponse.RecipientSnapshotResponse());
        } else {
            // For non-DRAFT invoices, fetch snapshot data
            return snapshotService.getRecipientSnapshotByHash(invoice.getRecipientSnapshotHash());
        }
    }

    /**
     * Fetches bank details snapshot for email generation.
     * For DRAFT invoices, fetches live data from BankDetails.
     * For non-DRAFT invoices, fetches snapshot data by hash.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse fetchBankDetailsSnapshot(InvoiceResponse invoice) {
        if (InvoiceStatus.draft.equals(invoice.getStatus())) {
            // For DRAFT invoices, fetch live data
            if (invoice.getBankDetailsId() == null) {
                return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse();
            }

            return bankDetailsRepository.findByIdAndAccountId(invoice.getBankDetailsId(), invoice.getAccountId())
                    .map(this::convertBankDetailsToBankDetailsResponse)
                    .orElse(new InvoiceSnapshotResponse.BankDetailsSnapshotResponse());
        } else {
            // For non-DRAFT invoices, fetch snapshot data
            return snapshotService.getBankDetailsSnapshotByHash(invoice.getBankDetailsSnapshotHash());
        }
    }

    /**
     * Converts AccountCompany to IssuerSnapshotResponse.
     */
    private InvoiceSnapshotResponse.IssuerSnapshotResponse convertAccountCompanyToIssuerResponse(org.jooq.generated.tables.pojos.AccountCompany company) {
        return new InvoiceSnapshotResponse.IssuerSnapshotResponse(
                company.getId(),
                company.getCompanyName(),
                company.getAddressStreet(),
                company.getAddressCity(),
                company.getAddressPostalCode(),
                company.getAddressCountry(),
                company.getVatNumber(),
                company.getRegistrationNumber(),
                company.getPhone(),
                company.getEmail(),
                company.getWebsite()
        );
    }

    /**
     * Converts Brand to RecipientSnapshotResponse.
     */
    private InvoiceSnapshotResponse.RecipientSnapshotResponse convertBrandToRecipientResponse(org.jooq.generated.tables.pojos.Brand brand) {
        return new InvoiceSnapshotResponse.RecipientSnapshotResponse(
                brand.getId(),
                brand.getName(),
                brand.getAddressStreet(),
                brand.getAddressCity(),
                brand.getAddressPostalCode(),
                brand.getAddressCountry(),
                brand.getVatNumber(),
                brand.getRegistrationNumber(),
                brand.getEmail(),
                brand.getPhone()
        );
    }

    /**
     * Converts BankDetails to BankDetailsSnapshotResponse.
     */
    private InvoiceSnapshotResponse.BankDetailsSnapshotResponse convertBankDetailsToBankDetailsResponse(org.jooq.generated.tables.pojos.BankDetails bankDetails) {
        return new InvoiceSnapshotResponse.BankDetailsSnapshotResponse(
                bankDetails.getId(),
                bankDetails.getName(),
                bankDetails.getBankName(),
                bankDetails.getIban(),
                bankDetails.getBicswift()
        );
    }

    /**
     * Checks if an invoice has been sent successfully at least once.
     *
     * @param invoiceId the invoice ID
     * @return true if the invoice has been sent successfully
     */
    private boolean hasBeenSentSuccessfully(Long invoiceId) {
        return deliveryLogRepository.countSuccessfulDeliveries(invoiceId) > 0;
    }
}
