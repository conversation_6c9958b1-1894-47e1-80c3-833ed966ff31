package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.ReviewStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Lightweight response DTO for post list items.
 * Contains only essential information for list views to avoid N+1 queries.
 */
public class PostListItemResponse {

    private Long id;
    private String caption;

    @JsonProperty("media_count")
    private int mediaCount;

    @JsonProperty("media_uris")
    private List<MediaItem> mediaUris;

    @JsonProperty("review_status")
    private ReviewStatus reviewStatus;

    private PostCreator creator;

    @JsonProperty("my_review_status")
    private ReviewStatus myReviewStatus;

    @JsonProperty("comment_count")
    private int commentCount;

    @JsonProperty("assigned_reviewer_count")
    private int assignedReviewerCount;

    @JsonProperty("approved_by_count")
    private int approvedByCount;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("can_edit")
    private boolean canEdit;

    @JsonProperty("can_review")
    private boolean canReview;

    @JsonProperty("can_comment")
    private boolean canComment;

    @JsonProperty("is_assigned_reviewer")
    private boolean isAssignedReviewer;

    @JsonProperty("has_reviewer_role")
    private boolean hasReviewerRole;

    @JsonProperty("user_role")
    private String userRole;

    public PostListItemResponse() {}

    public PostListItemResponse(Long id, String caption, int mediaCount, List<MediaItem> mediaUris,
                               ReviewStatus reviewStatus, PostCreator creator, ReviewStatus myReviewStatus,
                               int commentCount, int assignedReviewerCount, int approvedByCount,
                               LocalDateTime createdAt, boolean canEdit, boolean canReview, boolean canComment,
                               boolean isAssignedReviewer, boolean hasReviewerRole, String userRole) {
        this.id = id;
        this.caption = caption;
        this.mediaCount = mediaCount;
        this.mediaUris = mediaUris != null ? mediaUris : List.of();
        this.reviewStatus = reviewStatus;
        this.creator = creator;
        this.myReviewStatus = myReviewStatus;
        this.commentCount = commentCount;
        this.assignedReviewerCount = assignedReviewerCount;
        this.approvedByCount = approvedByCount;
        this.createdAt = createdAt;
        this.canEdit = canEdit;
        this.canReview = canReview;
        this.canComment = canComment;
        this.isAssignedReviewer = isAssignedReviewer;
        this.hasReviewerRole = hasReviewerRole;
        this.userRole = userRole;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public int getMediaCount() {
        return mediaCount;
    }

    public void setMediaCount(int mediaCount) {
        this.mediaCount = mediaCount;
    }

    public List<MediaItem> getMediaUris() {
        return mediaUris;
    }

    public void setMediaUris(List<MediaItem> mediaUris) {
        this.mediaUris = mediaUris;
    }

    public ReviewStatus getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(ReviewStatus reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public PostCreator getCreator() {
        return creator;
    }

    public void setCreator(PostCreator creator) {
        this.creator = creator;
    }

    public ReviewStatus getMyReviewStatus() {
        return myReviewStatus;
    }

    public void setMyReviewStatus(ReviewStatus myReviewStatus) {
        this.myReviewStatus = myReviewStatus;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public int getAssignedReviewerCount() {
        return assignedReviewerCount;
    }

    public void setAssignedReviewerCount(int assignedReviewerCount) {
        this.assignedReviewerCount = assignedReviewerCount;
    }

    public int getApprovedByCount() {
        return approvedByCount;
    }

    public void setApprovedByCount(int approvedByCount) {
        this.approvedByCount = approvedByCount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public boolean isCanEdit() {
        return canEdit;
    }

    public void setCanEdit(boolean canEdit) {
        this.canEdit = canEdit;
    }

    public boolean isCanReview() {
        return canReview;
    }

    public void setCanReview(boolean canReview) {
        this.canReview = canReview;
    }

    public boolean isCanComment() {
        return canComment;
    }

    public void setCanComment(boolean canComment) {
        this.canComment = canComment;
    }

    public boolean isAssignedReviewer() {
        return isAssignedReviewer;
    }

    public void setAssignedReviewer(boolean assignedReviewer) {
        isAssignedReviewer = assignedReviewer;
    }

    public boolean isHasReviewerRole() {
        return hasReviewerRole;
    }

    public void setHasReviewerRole(boolean hasReviewerRole) {
        this.hasReviewerRole = hasReviewerRole;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    /**
     * Nested class representing the post creator.
     */
    public static class PostCreator {
        private Long id;
        private String name;
        private String email;

        public PostCreator() {}

        public PostCreator(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    @Override
    public String toString() {
        return "PostListItemResponse{" +
                "id=" + id +
                ", caption='" + caption + '\'' +
                ", mediaCount=" + mediaCount +
                ", mediaUris=" + mediaUris +
                ", reviewStatus=" + reviewStatus +
                ", creator=" + creator +
                ", myReviewStatus=" + myReviewStatus +
                ", commentCount=" + commentCount +
                ", assignedReviewerCount=" + assignedReviewerCount +
                ", approvedByCount=" + approvedByCount +
                ", createdAt=" + createdAt +
                ", canEdit=" + canEdit +
                ", canReview=" + canReview +
                ", canComment=" + canComment +
                ", isAssignedReviewer=" + isAssignedReviewer +
                ", hasReviewerRole=" + hasReviewerRole +
                ", userRole='" + userRole + '\'' +
                '}';
    }
}
