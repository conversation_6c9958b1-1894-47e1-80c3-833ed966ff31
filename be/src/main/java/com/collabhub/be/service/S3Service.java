package com.collabhub.be.service;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.InternalServerErrorException;
import com.collabhub.be.modules.posts.dto.PresignedUploadResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Service for handling S3 file operations.
 * Provides secure file upload, bucket management, and URL generation.
 */
@Service
public class S3Service {

    private static final Logger logger = LoggerFactory.getLogger(S3Service.class);
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB

    // Magic numbers for file type validation
    private static final Map<String, byte[]> MAGIC_NUMBERS = new HashMap<>();
    static {
        // Image magic numbers
        MAGIC_NUMBERS.put("image/jpeg", new byte[]{(byte) 0xFF, (byte) 0xD8, (byte) 0xFF});
        MAGIC_NUMBERS.put("image/png", new byte[]{(byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A});
        MAGIC_NUMBERS.put("image/gif", new byte[]{0x47, 0x49, 0x46, 0x38});
        MAGIC_NUMBERS.put("image/webp", new byte[]{0x52, 0x49, 0x46, 0x46}); // RIFF header

        // Video magic numbers
        MAGIC_NUMBERS.put("video/mp4", new byte[]{0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70}); // ftyp
        MAGIC_NUMBERS.put("video/quicktime", new byte[]{0x00, 0x00, 0x00, 0x14, 0x66, 0x74, 0x79, 0x70}); // ftyp
        MAGIC_NUMBERS.put("video/x-msvideo", new byte[]{0x52, 0x49, 0x46, 0x46}); // RIFF header for AVI
    }

    private final S3Client s3Client;
    private final S3Presigner s3Presigner;
    private final S3Properties s3Properties;

    public S3Service(S3Client s3Client, S3Presigner s3Presigner, S3Properties s3Properties) {
        this.s3Client = s3Client;
        this.s3Presigner = s3Presigner;
        this.s3Properties = s3Properties;
    }

    /**
     * Ensures the shared bucket exists, creating it if necessary.
     * This method is account-agnostic since we use a single shared bucket.
     */
    public void ensureBucketExists() {
        String bucketName = s3Properties.getBucketName();

        try {
            // Check if bucket exists
            s3Client.headBucket(HeadBucketRequest.builder().bucket(bucketName).build());
            logger.debug("Bucket {} already exists", bucketName);
        } catch (NoSuchBucketException e) {
            // Bucket doesn't exist, create it
            logger.info("Creating shared bucket: {}", bucketName);
            createBucket(bucketName);
        } catch (Exception e) {
            logger.error("Error checking bucket existence: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR,
                "Failed to verify bucket existence: " + e.getMessage());
        }
    }

    /**
     * Uploads a file to S3 and returns the file URL.
     * Files are organized by account and resource type: {accountId}/{resourceType}/{filename}
     */
    public String uploadFile(MultipartFile file, Long accountId, String resourceType) {
        validateFile(file);
        ensureBucketExists();

        String bucketName = s3Properties.getBucketName();
        String fileName = generateFileName(file.getOriginalFilename());
        String keyPrefix = s3Properties.getKeyPrefix(accountId, resourceType);
        String key = keyPrefix + fileName;

        try {
            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(file.getContentType())
                    .contentLength(file.getSize())
                    .build();

            s3Client.putObject(putRequest, RequestBody.fromInputStream(file.getInputStream(), file.getSize()));

            String fileUrl = generateFileUrl(bucketName, key);
            logger.info("Successfully uploaded file: {} to bucket: {} (account: {}, type: {})",
                       fileName, bucketName, accountId, resourceType);
            return fileUrl;

        } catch (IOException e) {
            logger.error("Failed to read file content: {}", e.getMessage());
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Failed to read file content");
        } catch (Exception e) {
            logger.error("Failed to upload file to S3: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to upload file: " + e.getMessage());
        }
    }

    /**
     * Deletes a file from S3.
     * Validates that the file belongs to the specified account before deletion.
     */
    public void deleteFile(String fileUrl, Long accountId) {
        String bucketName = s3Properties.getBucketName();
        String key = extractKeyFromUrl(fileUrl);

        // Validate that the file belongs to the specified account
        if (!key.startsWith(accountId + "/")) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "File does not belong to the specified account");
        }

        try {
            DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.deleteObject(deleteRequest);
            logger.info("Successfully deleted file: {} from bucket: {} (account: {})",
                       key, bucketName, accountId);

        } catch (Exception e) {
            logger.error("Failed to delete file from S3: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to delete file: " + e.getMessage());
        }
    }

    /**
     * Generates a presigned URL for file download.
     * Files are organized by account and resource type: {accountId}/{resourceType}/{filename}
     */
    public String generatePresignedDownloadUrl(String fileUrl, Long accountId) {
        try {
            String bucketName = s3Properties.getBucketName();
            String key = extractKeyFromUrl(fileUrl);

            logger.debug("Generating presigned URL - Original URL: {}, Bucket: {}, Key: {}", fileUrl, bucketName, key);

            // Validate that the file belongs to the specified account
            if (!key.startsWith(accountId + "/")) {
                logger.warn("File does not belong to account {}: {}", accountId, key);
                throw new ForbiddenException(ErrorCode.ACCESS_DENIED, "File access denied");
            }

            // Check if object exists
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.headObject(headRequest);

            // Generate presigned download URL
            GetObjectRequest getRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofMinutes(15))
                    .getObjectRequest(getRequest)
                    .build();

            String presignedUrl = s3Presigner.presignGetObject(presignRequest).url().toString();

            logger.debug("Generated presigned download URL: {} for file: {} (account: {})", presignedUrl, key, accountId);
            return presignedUrl;

        } catch (Exception e) {
            logger.error("Failed to generate presigned download URL for fileUrl: {}, error: {}", fileUrl, e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to generate download URL");
        }
    }

    /**
     * Generates a presigned URL for file upload with enforced constraints.
     * Files are organized by account and resource type: {accountId}/{resourceType}/{filename}
     */
    public PresignedUploadResponse generatePresignedUploadUrl(Long accountId, String resourceType, String fileName,
                                                            String contentType, Long maxFileSize) {
        validateContentType(contentType);

        // Use provided max size or default
        long fileSizeLimit = maxFileSize != null ? maxFileSize : MAX_FILE_SIZE;

        ensureBucketExists();

        String bucketName = s3Properties.getBucketName();
        String keyPrefix = s3Properties.getKeyPrefix(accountId, resourceType);
        String key = keyPrefix + generateFileName(fileName);

        try {
            // Create presigned URL with constraints
            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .contentLength(fileSizeLimit) // Enforce max file size
                    .build();

            PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofMinutes(15))
                    .putObjectRequest(putRequest)
                    .build();

            String presignedUrl = s3Presigner.presignPutObject(presignRequest).url().toString();
            String finalUrl = generateFileUrl(bucketName, key);

            logger.info("Generated presigned URL for file: {} with constraints: type={}, maxSize={} (account: {}, resourceType: {})",
                       key, contentType, fileSizeLimit, accountId, resourceType);

            return new PresignedUploadResponse(presignedUrl, finalUrl, key, contentType, fileSizeLimit);

        } catch (Exception e) {
            logger.error("Failed to generate presigned URL: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to generate upload URL");
        }
    }

    /**
     * Validates that an uploaded file exists and meets constraints.
     * Should be called after presigned upload to verify the file was uploaded correctly.
     * Also validates that the file belongs to the specified account.
     */
    public boolean validateUploadedFile(String fileUrl, Long accountId, String expectedContentType, Long maxFileSize) {
        try {
            String bucketName = s3Properties.getBucketName();
            String key = extractKeyFromUrl(fileUrl);

            // Validate that the file belongs to the specified account
            if (!key.startsWith(accountId + "/")) {
                logger.warn("File does not belong to account {}: {}", accountId, key);
                return false;
            }

            // Check if object exists and get metadata
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            HeadObjectResponse headResponse = s3Client.headObject(headRequest);

            // Validate content type
            if (expectedContentType != null && !expectedContentType.equals(headResponse.contentType())) {
                logger.warn("Content type mismatch for file {}: expected {}, got {}",
                           key, expectedContentType, headResponse.contentType());
                return false;
            }

            // Validate file size
            if (maxFileSize != null && headResponse.contentLength() > maxFileSize) {
                logger.warn("File size exceeds limit for file {}: {} > {}",
                           key, headResponse.contentLength(), maxFileSize);
                return false;
            }

            logger.debug("File validation successful for: {} (account: {})", key, accountId);
            return true;

        } catch (Exception e) {
            logger.error("Failed to validate uploaded file: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Enhanced file validation that returns detailed validation information.
     */
    public com.collabhub.be.modules.posts.dto.FileValidationResponse validateUploadedFileDetailed(
            String fileUrl, Long accountId, String expectedContentType, Long maxFileSize) {

        try {
            String bucketName = s3Properties.getBucketName();
            String key = extractKeyFromUrl(fileUrl);

            // Validate that the file belongs to the specified account
            if (!key.startsWith(accountId + "/")) {
                logger.warn("File does not belong to account {}: {}", accountId, key);
                com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails details =
                    new com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails(
                        false, false, false, maxFileSize, expectedContentType);

                return new com.collabhub.be.modules.posts.dto.FileValidationResponse(
                    false, fileUrl, "File does not belong to the specified account", null, null, details);
            }

            // Check if object exists and get metadata
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            HeadObjectResponse headResponse = s3Client.headObject(headRequest);

            boolean fileExists = true;
            boolean sizeCheckPassed = true;
            boolean contentTypeCheckPassed = true;
            String message = "File validation successful";

            // Validate content type
            if (expectedContentType != null && !expectedContentType.equals(headResponse.contentType())) {
                contentTypeCheckPassed = false;
                message = "Content type mismatch: expected " + expectedContentType + ", got " + headResponse.contentType();
                logger.warn("Content type mismatch for file {}: expected {}, got {}",
                           key, expectedContentType, headResponse.contentType());
            }

            // Validate file size
            if (maxFileSize != null && headResponse.contentLength() > maxFileSize) {
                sizeCheckPassed = false;
                message = "File size exceeds limit: " + headResponse.contentLength() + " > " + maxFileSize;
                logger.warn("File size exceeds limit for file {}: {} > {}",
                           key, headResponse.contentLength(), maxFileSize);
            }

            boolean isValid = sizeCheckPassed && contentTypeCheckPassed;

            com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails details =
                new com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails(
                    sizeCheckPassed, contentTypeCheckPassed, fileExists, maxFileSize, expectedContentType);

            logger.debug("File validation completed for: {} - Valid: {}", key, isValid);

            return new com.collabhub.be.modules.posts.dto.FileValidationResponse(
                isValid, fileUrl, message, headResponse.contentLength(),
                headResponse.contentType(), details);

        } catch (NoSuchKeyException e) {
            logger.warn("File not found: {}", fileUrl);
            com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails details =
                new com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails(
                    false, false, false, maxFileSize, expectedContentType);

            return new com.collabhub.be.modules.posts.dto.FileValidationResponse(
                false, fileUrl, "File not found", null, null, details);

        } catch (Exception e) {
            logger.error("Failed to validate uploaded file: {}", e.getMessage());
            com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails details =
                new com.collabhub.be.modules.posts.dto.FileValidationResponse.ValidationDetails(
                    false, false, false, maxFileSize, expectedContentType);

            return new com.collabhub.be.modules.posts.dto.FileValidationResponse(
                false, fileUrl, "Validation failed: " + e.getMessage(), null, null, details);
        }
    }

    private void createBucket(String bucketName) {
        try {
            CreateBucketRequest createRequest = CreateBucketRequest.builder()
                    .bucket(bucketName)
                    .build();

            s3Client.createBucket(createRequest);
            logger.info("Successfully created bucket: {}", bucketName);

        } catch (Exception e) {
            logger.error("Failed to create bucket: {}", e.getMessage());
            throw new InternalServerErrorException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to create bucket: " + e.getMessage());
        }
    }

    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "File is required");
        }

        String originalFilename = file.getOriginalFilename();
        String contentType = file.getContentType();

        // Validate filename
        validateFilename(originalFilename);

        // Validate content type
        validateContentType(contentType);

        // Validate file size based on content type
        validateFileSize(file.getSize(), contentType);

        // Validate file content matches declared type
        validateFileContent(file, contentType);
    }

    private void validateContentType(String contentType) {
        if (contentType == null || !s3Properties.isAllowedMimeType(contentType)) {
            throw new BadRequestException(ErrorCode.FILE_TYPE_NOT_ALLOWED,
                "Unsupported file type. Allowed types: " + s3Properties.getAllowedMimeTypes());
        }
    }

    private void validateFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.FILE_NAME_INVALID, "Filename is required");
        }

        // Check for blocked extensions
        if (s3Properties.isBlockedExtension(filename)) {
            throw new BadRequestException(ErrorCode.FILE_TYPE_NOT_ALLOWED,
                "File extension is not allowed for security reasons");
        }

        // Check for allowed extensions
        if (!s3Properties.isAllowedExtension(filename)) {
            throw new BadRequestException(ErrorCode.FILE_TYPE_NOT_ALLOWED,
                "File extension is not allowed. Allowed extensions: " + s3Properties.getAllowedExtensions());
        }

        // Check for directory traversal attempts
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            throw new BadRequestException(ErrorCode.FILE_NAME_INVALID,
                "Filename contains invalid characters");
        }
    }

    private void validateFileSize(long fileSize, String contentType) {
        long maxSize = s3Properties.getMaxFileSizeForType(contentType);

        if (fileSize > maxSize) {
            String fileType = s3Properties.isImageType(contentType) ? "image" : "video";
            throw new BadRequestException(ErrorCode.FILE_SIZE_EXCEEDED,
                String.format("File size exceeds maximum allowed size for %s files: %d MB",
                    fileType, maxSize / 1024 / 1024));
        }
    }

    private void validateFileContent(MultipartFile file, String contentType) {
        try {
            byte[] magicNumber = MAGIC_NUMBERS.get(contentType);
            if (magicNumber != null) {
                byte[] fileHeader = new byte[magicNumber.length];
                try (InputStream inputStream = file.getInputStream()) {
                    int bytesRead = inputStream.read(fileHeader);
                    if (bytesRead < magicNumber.length) {
                        throw new BadRequestException(ErrorCode.FILE_CONTENT_INVALID,
                            "File content is too small or corrupted");
                    }

                    if (!Arrays.equals(fileHeader, magicNumber)) {
                        throw new BadRequestException(ErrorCode.FILE_CONTENT_INVALID,
                            "File content does not match declared content type");
                    }
                }
            }
        } catch (IOException e) {
            logger.error("Failed to validate file content: {}", e.getMessage());
            throw new BadRequestException(ErrorCode.FILE_CONTENT_INVALID,
                "Failed to validate file content");
        }
    }

    private String generateFileName(String originalFilename) {
        String sanitizedFilename = s3Properties.sanitizeFilename(originalFilename);
        String extension = "";
        if (sanitizedFilename != null && sanitizedFilename.contains(".")) {
            extension = sanitizedFilename.substring(sanitizedFilename.lastIndexOf("."));
        }
        return UUID.randomUUID().toString() + extension;
    }

    /**
     * Generates a file URL from bucket name and key.
     * Made public to support media URL generation.
     */
    public String generateFileUrl(String bucketName, String key) {
        if (s3Properties.getEndpoint() != null) {
            // For MinIO or custom S3 endpoint
            return s3Properties.getEndpoint() + "/" + bucketName + "/" + key;
        } else {
            // For AWS S3
            return "https://" + bucketName + ".s3." + s3Properties.getRegion() + ".amazonaws.com/" + key;
        }
    }

    /**
     * Gets the configured S3 bucket name.
     */
    public String getBucketName() {
        return s3Properties.getBucketName();
    }

    private String extractKeyFromUrl(String fileUrl) {
        // Extract key from URL (everything after bucket name, excluding query parameters)
        String bucketName = s3Properties.getBucketName();

        // Remove query parameters first
        String urlWithoutQuery = fileUrl;
        int queryIndex = fileUrl.indexOf('?');
        if (queryIndex != -1) {
            urlWithoutQuery = fileUrl.substring(0, queryIndex);
        }

        int bucketIndex = urlWithoutQuery.indexOf(bucketName);
        if (bucketIndex == -1) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Invalid file URL - bucket not found");
        }

        // Find the start of the key (after bucket name and slash)
        int keyStartIndex = bucketIndex + bucketName.length() + 1;
        if (keyStartIndex >= urlWithoutQuery.length()) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT, "Invalid file URL - no key found");
        }

        return urlWithoutQuery.substring(keyStartIndex);
    }
}
